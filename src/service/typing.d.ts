declare namespace API {
  interface PageParams {
    size: number;
    page: number;
  }

  interface Result<T> {
    data: T;
  }

  type Success = null;

  // 与后端约定的响应数据格式
  interface Response {
    code: string | number;
    msg: string;
    data: any;

    // 这2个是二维码php接口格式的数据
    status: number;

    result: any;
  }

  type FileType =
    | {
        file: File;

        url: string;
      }
    | string;
}

declare namespace FormConfig {
  type Path = string | number;
}
