import type { FeatureCollection, Geometry } from "./geojson";

/**
 * 渐变配置
 */
export interface LinearConfig {
  /**
   * 角度
   */
  angle: number;

  /**
   * 开始结束位置配置
   */
  colorStops: {
    offset: number;
    color: string;
  }[];
}

export interface GeoMercatorParams {
  /**
   * geojson
   */
  data: FeatureCollection<Geometry>;

  /**
   * 多边形质心
   */
  center: number[];

  /**
   * 缩放大小 中国:960w = 1
   */
  scale: number;
}

export interface JsonParseOptions<T> {
  defaultValue?: T;
  reviver?: (key: any, value: any) => any;
}

export interface CoordinateItem {
  /**
   * 起点坐标x
   */
  x0: number;

  /**
   * 起点坐标y
   */
  y0: number;

  /**
   * 终点坐标x
   */
  x1: number;

  /**
   * 终点坐标y
   */
  y1: number;
}
