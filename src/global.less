@import "../public/css/font.css";

body,
html {
  font-size: 16px;
  color: #c5d0d4;

  img {
    max-width: initial;
  }
}

.ant-table-wrapper .ant-table-thead > tr > th {
  &::before {
    display: none !important;
  }
}

.ant-picker-range-wrapper {
  border: 1px solid #00b4ff;
  border-radius: 8px;
  text-align: center;
}

.ant-select-item {
  text-align: center !important;
}

.ant-select-dropdown {
  background: linear-gradient(
    0deg,
    rgba(2, 29, 58, 0.96) 0%,
    rgba(2, 48, 84, 0.96) 100%
  );
  border: 1px solid #00b4ff;
}

@keyframes moveOpacity {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  50% {
    opacity: 0.8;
    transform: translateY(6px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 逆时针旋转
@keyframes rotateCounterClockwise {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(-365deg);
    transform: rotate(-365deg);
  }
}

// 顺时针旋转
@keyframes rotateClockwise {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(365deg);
    transform: rotate(365deg);
  }
}

// 上下移动
@keyframes moveUpDown {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
    opacity: 1;
  }

  25% {
    -webkit-transform: translateY(5px);
    transform: translateY(5px);
    opacity: 0.9;
  }

  50% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 0.8;
  }

  75% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
    opacity: 0.9;
  }

  to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes floorKeyframes1 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 1;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 0;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes2 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 1;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes3 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 0;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes4 {
  0% {
    opacity: 0;
  }
  33% {
    opacity: 1;
  }
  60% {
    opacity: 0;
  }
  66% {
    opacity: 0;
  }
  72% {
    opacity: 0;
  }
  95% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes5 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 1;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes6 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 0;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.floor-hover {
  cursor: pointer;
  z-index: 10;
  position: relative;

  &:hover {
    opacity: 1 !important;
    animation-play-state: paused !important;
  }
}
