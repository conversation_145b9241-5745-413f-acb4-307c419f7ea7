import { useCountUp } from "use-count-up";
import { useDeepCompareEffect } from "ahooks";
import lodash from "lodash-es";
import { useMemo } from "react";

export interface JumpValueProps {
  decimals?: number; // 小数位数
  dataValue: number; // 值
  omitZero?: boolean; // 尾0是否省略

  // 动画时长
  duration?: number;

  /**
   * 动画间隔
   */
  animationInterval?: number;
}
function useJumpValue(props: JumpValueProps) {
  const {
    dataValue,
    decimals = 0,
    omitZero = true,
    duration = 1,
    animationInterval,
  } = props;

  const decimalPlaces = useMemo(() => {
    if (omitZero) {
      // 尾零省略
      return lodash.toNumber(dataValue).toString().split(".")?.[1]?.length;
    }
    return decimals;
  }, [dataValue, decimals, omitZero]);

  const { value, reset } = useCountUp({
    start: 0,
    end: dataValue,
    duration,
    // 小数几位
    decimalPlaces,
    isCounting: true,
    onComplete: () => {
      return {
        shouldRepeat: !lodash.isUndefined(animationInterval),
        delay: animationInterval,
      };
    },
  });

  useDeepCompareEffect(() => {
    reset();
  }, [duration, dataValue, animationInterval]);

  return lodash.toNumber(value);
}
export default useJumpValue;
