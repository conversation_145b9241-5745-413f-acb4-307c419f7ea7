import { useMemo } from "react";

interface IProps {
  height: number;

  headerHeight: number;

  rowGap: number;

  // 展示列表数据的个数
  size: number;

  // 其它间距需要去除的 例如与header需要拉开间距
  afterHeight?: number;
}
function useListSize(props: IProps) {
  const { headerHeight, height, size, rowGap } = props;

  const itemHeight = useMemo(() => {
    return (height - headerHeight) / size - (rowGap * (size - 1)) / size;
  }, [height, headerHeight, rowGap, size]);

  const listHeight = useMemo(() => {
    return height - headerHeight;
  }, [headerHeight, height]);

  return { itemHeight, listHeight };
}

export default useListSize;
