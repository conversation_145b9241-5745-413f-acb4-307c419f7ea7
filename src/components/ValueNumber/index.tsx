import type { JumpValueProps } from "@/hooks/useJumpValue";
import useJumpValue from "@/hooks/useJumpValue";
import { chunk as _chunk } from "lodash-es";
import React, { useMemo } from "react";

interface IProps extends JumpValueProps {
  splitCount?: number;

  splitStr?: string;
}
function ValueNumber(props: IProps) {
  const { splitCount = 3, splitStr = "," } = props;
  const value = useJumpValue(props);

  const valueList = useMemo(() => {
    const list = value.toString()?.split("")?.reverse() ?? [];
    return _chunk(list, splitCount)
      ?.map((val) => val.reverse())
      .reverse();
  }, [splitCount, value]);

  const valueTotal = valueList.length ?? 0;

  return (
    <React.Fragment>
      {valueList.map((val, index) => (
        <React.Fragment key={`value${index}`}>
          {val}
          {valueTotal - 1 !== index ? splitStr : ""}
        </React.Fragment>
      ))}
    </React.Fragment>
  );
}
export default ValueNumber;
