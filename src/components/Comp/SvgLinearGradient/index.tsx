import type { LinearConfig } from "@/typings";

interface IProps {
  id: string;

  config: LinearConfig;
}
function SvgLinearGradient(props: IProps) {
  const {
    id,
    config: { colorStops, angle },
  } = props;

  return (
    <linearGradient
      id={id}
      x1={0}
      x2={0}
      y1={0}
      y2={1}
      gradientTransform={`rotate(${180 + angle}, .5, .5)`}
    >
      {colorStops.map((item, index) => (
        <stop
          key={`stop${index + 1}`}
          offset={item.offset}
          stopColor={item.color}
        />
      ))}
    </linearGradient>
  );
}
export default SvgLinearGradient;
