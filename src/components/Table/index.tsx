import type {
  ActionType,
  ProColumns,
  ProTableProps,
} from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { SearchConfig } from "@ant-design/pro-table/es/components/Form/FormRender";
import { size as _size } from "lodash-es";
import { nanoid } from "nanoid";
import { ConfigProvider, type GetProp } from "antd";
import { useMemo, useRef, useState } from "react";
import styles from "./index.module.less";

const handleValueEnum = (valueEnum: any) => {
  const result: any = {};
  if (Array.isArray(valueEnum)) {
    for (const val of valueEnum) {
      result[val.value] = {
        text: val.label,
        status: val.status,
      };
    }
    return result;
  }
  return valueEnum;
};

const align = "left";

type IProps = ProTableProps<any, any> & {
  searchColumns?: ProColumns<any, "text">[] | undefined;
  filterHide?: boolean;
  collapseHide?: boolean;
};
export default (props: IProps) => {
  const {
    request,
    columns: columnsBase,
    actionRef,
    searchColumns = [],
    columnEmptyText = "--",
    collapseHide = false,
    options,
    ...rest
  } = props;
  const ref = (actionRef ?? useRef<ActionType>()) as React.MutableRefObject<
    ActionType | undefined
  >;

  const [loadEmpty, setLoadEmpty] = useState(true);

  const onRequest: GetProp<typeof ProTable, "request"> = async (
    params,
    sort,
    filter
  ) => {
    if (!request) {
      return {};
    }
    const { pageSize, current, ...rest } = params;
    const paramsBase = {
      ...rest,
      size: pageSize,
      page: current,
    };
    try {
      const result = await request(paramsBase, sort, filter);
      const data = result?.data ?? [];
      const total = result.total ?? 0;
      setLoadEmpty(total === 0 && _size(data) === 0);
      return {
        data,
        total,
        success: result.success ?? true,
      };
    } catch (error) {
      return {};
    }
  };

  const isEmpty = useMemo(() => {
    return request ? loadEmpty : _size(props.dataSource) === 0;
  }, [loadEmpty, props.dataSource, request]);

  const columns = useMemo(() => {
    const searchList =
      searchColumns?.map((draft: any) => {
        const val = draft;
        if (val?.valueEnum) {
          val.valueEnum = handleValueEnum(val.valueEnum);
        }
        return {
          ...val,
          hideInTable: true,
        };
      }) ?? [];

    const baseList =
      columnsBase?.map((draft: any) => {
        const val = draft;
        const hideInSearch = true;
        if (val?.valueEnum) {
          val.valueEnum = handleValueEnum(val.valueEnum);
        }
        if (val.dataIndex === "dataIndex") {
          return {
            title: "序号",
            align,
            width: 80,
            hideInSearch,
            ...val,
            render: (_val: any, _item: any, index: number) => {
              if (ref.current?.pageInfo) {
                const { pageInfo } = ref.current;
                const { current, pageSize } = pageInfo;
                const domIndex = (current - 1) * pageSize + index + 1;
                return domIndex < 10 ? `0${domIndex}` : domIndex;
              }
              return index;
            },
          };
        }
        return {
          align,
          hideInSearch,
          width: isEmpty ? _size(val.title) * 16 + 30 : val.width,
          ...(!val.render && { ellipsis: isEmpty ? false : true }),
          ...val,
        };
      }) ?? [];
    return [...searchList, ...baseList];
  }, [columnsBase, isEmpty, ref, searchColumns]);

  const [collapsed, setCollapsed] = useState(false);

  const search: false | SearchConfig = useMemo(() => {
    if (searchColumns?.length > 0) {
      return {
        collapsed,
        onCollapse: (e) => {
          setCollapsed(e);
        },
        collapseRender: collapseHide ? () => null : undefined,
      };
    }
    return false;
  }, [searchColumns?.length, collapsed, collapseHide]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBg: "rgba(28,59,104,.45)",
            colorBgContainer: "transparent",
            borderColor: "rgba(201, 201, 201,.15)",
            fontSize: 16,
            cellPaddingBlockMD: 13,
            cellPaddingInlineMD: 8,
          },
          Typography: {
            fontSize: 18,
          },
          Pagination:{
            itemActiveBg:"#165184",
            colorPrimary:"#3678BC"
          }
        },
      }}
    >
      <ProTable
        actionRef={ref}
        columns={columns}
        request={onRequest}
        columnEmptyText={columnEmptyText}
        search={search}
        rowKey={() => nanoid()}
        options={{
          fullScreen: false,
          reload: false,
          setting: false,
          density: false,
          ...options,
        }}
        rootClassName={styles.proTable}
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
          style: {
            background: "transparent",
          },
        }}
        scroll={{
          scrollToFirstRowOnChange: true,
        }}
        pagination={{
          defaultCurrent: 1,
          defaultPageSize: 10,
          //   showQuickJumper: true,
          style: {
            marginTop: 18,
          },
          showTotal: (total: number) => <span>共 {total} 条记录</span>,
          size: "default",
        }}
        {...rest}
      ></ProTable>
    </ConfigProvider>
  );
};
