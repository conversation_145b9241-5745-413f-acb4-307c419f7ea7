import { nanoid } from "nanoid";
import React, { useRef } from "react";

interface CurvedTextProps {
  value: string;

  className?: string;
}

const CurvedText: React.FC<CurvedTextProps> = (props) => {
  const { value, className } = props;

  const pathId = useRef(nanoid());

  // 弧高
  const arcHeight = 30;

  return (
    <svg className={className} width={220} height={110} viewBox="0 0 240 110">
      <defs>
        <path
          id={pathId.current}
          d={`M 0 0 A 100 ${arcHeight} 0 1 0 240 0`}
        ></path>
      </defs>
      <text fontSize={23}>
        <textPath
          href={`#${pathId.current}`}
          startOffset="50%"
          textAnchor="middle"
          fill="rgba(255,255,255,0.85)"
          fontWeight="bold"
          letterSpacing={2}
        >
          {value}
        </textPath>
      </text>
    </svg>
  );
};

export default CurvedText;
