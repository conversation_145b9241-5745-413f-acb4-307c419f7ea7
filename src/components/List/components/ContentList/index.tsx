import { motion } from "framer-motion";
import Container from "../../container";
import ListItem from "../ListItem";
import useListAnimate from "@/hooks/useListAnimate";

function ContentList() {
  const { width, listHeight, rowGap, itemHeight, data, animationConfig, size } =
    Container.useContainer();

  const {
    controls,
    firstControls,
    dataIndexList,
    showAnimate,
    beforeIndexList,
  } = useListAnimate({
    data,
    itemHeight,
    rowGap,
    listHeight,
    animationConfig,
    size,
  });

  return (
    <div
      className="overflow-hidden relative"
      style={{
        height: listHeight,
        width,
      }}
    >
      <motion.div
        className="absolute"
        style={{
          width,
          height: listHeight,
        }}
        animate={controls}
      >
        {showAnimate && (
          <motion.div animate={firstControls}>
            {beforeIndexList?.map((val) => (
              <ListItem key={`before${val}`} index={val} />
            ))}
          </motion.div>
        )}
        {dataIndexList?.map((val) => <ListItem key={val} index={val} />)}
      </motion.div>
    </div>
  );
}
export default ContentList;
