import React, { useMemo } from "react";
import Container from "../../container";
import { get as _get } from "lodash-es";
import { Typography } from "antd";

const { Text } = Typography;

interface IProps {
  index: number;
}
function ListItem(props: IProps) {
  const { index } = props;
  const { data, columns, itemHeight, rowGap } = Container.useContainer();

  const dataItem = useMemo(() => {
    return data[index];
  }, [data, index]);

  return (
    <div
      className="flex w-full items-center bg-[rgba(40,62,93,.2)] px-3 rounded-[2px]"
      style={{
        height: itemHeight,
        marginTop: rowGap,
      }}
    >
      {columns?.map((val, valIndex) => {
        const value = val.key ? _get(dataItem, val.dataIndex) : undefined;
        // 序号
        const isIndex = val.dataIndex === "dataIndex";
        return (
          <div
            key={val.key ?? valIndex}
            className={`truncate color-text px-[6px] ${val.className}`}
            style={{
              flex: val.width ? `${val.width}px` : 1,
              maxWidth: val.width,
              textAlign: val.align,
            }}
          >
            {isIndex && (
              <React.Fragment>
                {index + 1 < 10 ? 0 : ""}
                {index + 1}
              </React.Fragment>
            )}
            {!isIndex && (
              <React.Fragment>
                {val.render ? (
                  (val.render?.(value, dataItem, index) as React.ReactNode)
                ) : (
                  <Text className="w-full" ellipsis={{ tooltip: value }}>
                    {value}
                  </Text>
                )}
              </React.Fragment>
            )}
          </div>
        );
      })}
    </div>
  );
}
export default ListItem;
