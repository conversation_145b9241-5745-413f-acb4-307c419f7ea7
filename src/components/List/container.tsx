import { createContainer } from "unstated-next";
import { useMemo } from "react";
import type { ColumnType } from "antd/es/table";
import {
  TableAnimationConnectModeEnum,
  TableAnimationModeEnum,
  TableAnimationTypeEnum,
} from "@/enum";

export interface IProps {
  width: number;

  height: number;

  columns: ColumnType<Record<string, any>>[];

  data: any[];

  /**
   * 表头高度
   */
  headerHeight: number;

  /**
   * 行数
   */
  size: number;
}

function useContainer(props: IProps) {
  const { height, columns, data, width, headerHeight, size } = props;

  const listHeight = useMemo(() => {
    return height - headerHeight;
  }, [headerHeight, height]);

  // 行间距
  const rowGap = 3;

  const itemHeight = useMemo(() => {
    return (height - headerHeight) / size - (rowGap * (size - 1)) / size;
  }, [height, headerHeight, rowGap, size]);

  const animationConfig = {
    show: true, // 是否显示动画
    type: TableAnimationTypeEnum.Single, // 类型
    connectMode: TableAnimationConnectModeEnum.Continuous, // 衔接方式
    animateMode: TableAnimationModeEnum.Flip, // 动画形式
    interval: 3, //
    backgroundFixed: false,
  };

  return {
    height,
    columns,
    data,
    width,
    headerHeight,
    listHeight,
    itemHeight,
    rowGap,
    size,
    animationConfig,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
