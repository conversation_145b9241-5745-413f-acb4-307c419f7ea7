import type { LinearConfig } from "@/typings";
import { nanoid } from "nanoid";
import React from "react";
import { useCallback, useMemo, useRef } from "react";
import { <PERSON>, <PERSON>C<PERSON>, CartesianGrid, XAxis, YAxis } from "recharts";
import SvgLinearGradient from "../Comp/SvgLinearGradient";

interface DataItem {
  name: string;

  value: number;
}

interface IProps {
  data: DataItem[];

  width: number;

  height: number;

  yAxisWidth?: number;
}

interface ColorConfig {
  top: LinearConfig;
  bottom: LinearConfig;
  center: LinearConfig;
}

function HorizontalBar(props: IProps) {
  const { data, width, height } = props;
  const customXTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;
    return (
      <g>
        <foreignObject
          style={{
            overflow: "visible",
          }}
          width="1"
          height="1"
          x={x}
          y={y + 5}
        >
          <div
            style={{
              color: "#c5d0d4",
            }}
          >
            <div
              style={{
                width: "fit-content",
                transform: `translate(-50%, 0px) rotate(0deg)`,
                textAlign: "center",
              }}
              className="truncate w-[40px]"
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const customYTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;

    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={x}
          y={y}
          style={{
            overflow: "visible",
          }}
        >
          <div
            style={{
              color: "#c5d0d4",
            }}
          >
            <div
              style={{
                width: "max-content",
                transform: `translate(-100%, -50%)`,
                textAlign: "right",
              }}
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const filterIdPrefix = useRef(nanoid());

  const colorList = useMemo(() => {
    const value: ColorConfig[] = [
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(253, 148, 95, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(247, 79, 64, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(253, 148, 95, 1)",
            },
            {
              offset: 1,
              color: "rgba(247, 79, 64, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(249, 93, 88, 1)",
            },
            {
              offset: 1,
              color: "rgba(253, 154, 133, 0)",
            },
          ],
        },
      },
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(247, 68, 168, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(215, 24, 80, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(247, 68, 168, 1)",
            },
            {
              offset: 1,
              color: "rgba(215, 24, 80, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(222, 34, 100, 1)",
            },
            {
              offset: 1,
              color: "rgba(249, 88, 191, 0)",
            },
          ],
        },
      },
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(193, 99, 247, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(174, 70, 247, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(193, 99, 247, 1)",
            },
            {
              offset: 1,
              color: "rgba(174, 70, 247, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(181, 77, 246, 1)",
            },
            {
              offset: 1,
              color: "rgba(204, 111, 248, 0)",
            },
          ],
        },
      },
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(63, 168, 242, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(21, 100, 232, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(63, 168, 242, 1)",
            },
            {
              offset: 1,
              color: "rgba(21, 100, 232, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(27, 110, 233, 1)",
            },
            {
              offset: 1,
              color: "rgba(66, 170, 242, 0)",
            },
          ],
        },
      },
    ];
    return value;
  }, []);

  const CustomBarShape = useCallback(
    (props: any) => {
      const shapeProps = props.shapeProps;
      const { x, y, width, height, index } = shapeProps;

      const skewAngle = 0; // 你的 skew 角度

      const verticalOffset =
        (width / 2) * Math.tan((skewAngle * Math.PI) / 180);

      const ellipseHeight = width / 4;

      const idIndex = index < 3 ? index : colorList.length - 1;

      const topFill = `url(#${filterIdPrefix.current}-${idIndex}-top)`;

      const centerFill = `url(#${filterIdPrefix.current}-${idIndex}-center)`;

      const bottomFill = `url(#${filterIdPrefix.current}-${idIndex}-bottom)`;

      return (
        <g>
          <g>
            <ellipse
              cx={x + width / 2}
              cy={y - verticalOffset * 2}
              rx={width / 2}
              ry={ellipseHeight}
              fill={topFill}
            ></ellipse>
            <rect
              width={width}
              height={height}
              x={x}
              y={y - verticalOffset}
              fill={centerFill}
            ></rect>
            <ellipse
              cx={x + width / 2}
              cy={y + height - verticalOffset * 2}
              rx={width / 2}
              ry={ellipseHeight}
              fill={bottomFill}
            ></ellipse>
          </g>
        </g>
      );
    },
    [colorList.length]
  );

  // 轴单位
  const customizedLabel = useCallback((props) => {
    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={props.viewBox.x}
          y={props.viewBox.y}
          style={{
            overflow: "visible",
          }}
        >
          <div
            style={{
              color: "#c5d0d4",
              width: "max-content",
              transform: "translate(-20px, -40px)",
            }}
          >
            (单位)
          </div>
        </foreignObject>
      </g>
    );
  }, []);

  return (
    <BarChart
      width={width}
      height={height}
      data={data}
      className="text-[14px]"
      margin={{
        top: 50,
        right: 45,
        left: 20,
        bottom: 10,
      }}
      barSize={20}
    >
      <CartesianGrid vertical={false} stroke="rgba(168, 168, 168, 0.2)" />
      <XAxis
        dataKey="name"
        tick={customXTick}
        tickLine={false}
        axisLine={{
          stroke: "rgba(201, 201, 201, 0.55)",
        }}
      />
      <YAxis
        tickLine={false}
        axisLine={false}
        tick={customYTick}
        width={20}
        label={customizedLabel}
      />
      {colorList.map((val, index) => {
        const idPrefix = `${filterIdPrefix.current}-${index}`;
        return (
          <defs key={idPrefix}>
            <SvgLinearGradient id={`${idPrefix}-top`} config={val.top} />
            <SvgLinearGradient id={`${idPrefix}-center`} config={val.center} />
            <SvgLinearGradient id={`${idPrefix}-bottom`} config={val.bottom} />
          </defs>
        );
      })}
      <Bar
        dataKey="value"
        barSize={30}
        fill="#1DB7FA"
        shape={(props: any) => {
          return <CustomBarShape shapeProps={props} />;
        }}
        label={false}
      />
    </BarChart>
  );
}
export default HorizontalBar;
