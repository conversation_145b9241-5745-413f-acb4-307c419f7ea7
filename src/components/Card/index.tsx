import React, { useMemo } from "react";
import subTitleBg1 from "@/assets/subTitle1.png";
import subTitleBg2 from "@/assets/subTitle2.png";
import subTitleBg3 from "@/assets/subTitle3.png";
import subTitleBg4 from "@/assets/subTitle4.png";
import type { TabsProps } from "antd";
import Title from "../Title";

interface IProps {
  /**
   * 卡片右上角的操作区域
   */
  extra?: React.ReactNode;

  /**
   * 标题
   */
  title: string | React.ReactNode;

  /**
   * 根据类型选择不同尺寸的背景图片
   */
  headerType: 1 | 2 | 3 | 4;

  /**
   * 切换的props
   */
  tabsProps?: TabsProps;

  /**
   * 查看更多
   */
  onMore?: () => void;
}
function Card(props: React.PropsWithChildren<IProps>) {
  const { title, children, headerType, extra, tabsProps, onMore } = props;

  const bgUrl = useMemo(() => {
    const config = {
      1: subTitleBg1,
      2: subTitleBg2,
      3: subTitleBg3,
      4: subTitleBg4,
    };
    return config[headerType];
  }, [headerType]);

  return (
    <div className="w-full h-full rounded-[12px] relative z-10">
      <div className="h-[40px] relative flex justify-between">
        <div
          className="absolute top-[20px] left-0 w-full h-[28px] bg-cover z-[-1]"
          style={{
            backgroundImage: `url(${bgUrl})`,
          }}
        ></div>
        <div
          className="text-[26px] pl-[40px]"
          style={{
            backgroundImage:
              "linear-gradient(180deg, #A6DDEF 0%, #F3FEFF 54%,#9EB7FF 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            fontFamily: "YouSheBiaoTiHei-2",
          }}
        >
          {title}
        </div>
        <div className="flex items-center mt-1">
          {onMore && (
            <Title
              className="text-[16px] pl-[50px] pr-[18px] cursor-pointer"
              onClick={onMore}
            >
              more
            </Title>
          )}
          {extra}
          {tabsProps && (
            <div className="flex items-center">
              {tabsProps.items?.map((val) => (
                <div
                  key={val.key}
                  onClick={() => tabsProps.onChange?.(val.key)}
                  className={`${tabsProps.activeKey === val.key ? "bg-[#165184] color-text" : "bg-[#27476A] color-secondary"} border border-[#3678BC] w-[54px] h-[26px] flex items-center justify-center cursor-pointer hover:bg-[#165184] color-text`}
                >
                  {val.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      <div className="py-4 pl-6 pr-2">{children}</div>
    </div>
  );
}
export default Card;
