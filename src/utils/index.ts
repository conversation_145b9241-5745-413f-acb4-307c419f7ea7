import type {
  GeoMercatorParams,
  LinearConfig,
  JsonParseOptions,
} from "@/typings";
import tinycolor from "tinycolor2";

export const formatLinear = (config: LinearConfig) => {
  const list =
    [...config.colorStops]?.sort((a, b) => {
      return a.offset - b.offset;
    }) ?? [];

  const str = list.map((val) => `${val.color} ${val.offset * 100}%`);

  return `linear-gradient(${config.angle}deg,${str.join(",")})`;
};

// 对feature做转换
export const transFromGeoJSON = (data: GeoMercatorParams["data"]) => {
  const worldData = data;
  const features = worldData?.features;
  for (let i = 0; i < features.length; i++) {
    const element = features[i];
    if (element.geometry.type === "Polygon") {
      element.geometry.coordinates = [element.geometry.coordinates] as any;
      element.geometry.type = "MultiPolygon";
    }
  }
  return worldData;
};

export function parseJSON<T = any>(
  jsonString?: string,
  options: JsonParseOptions<T> = {}
) {
  const { defaultValue = {}, reviver } = options;

  if (jsonString === undefined) {
    return undefined;
  }

  try {
    const parsedObj = JSON.parse(jsonString, reviver);
    if (typeof parsedObj !== "object" || parsedObj === null) {
      return defaultValue as T;
    }
    return parsedObj as T;
  } catch (err) {
    return defaultValue as T;
  }
}

// 获取hex颜色和透明度
export const transformColor = (color?: string) => {
  const copyColor = tinycolor(color ?? "#000");
  return {
    value: copyColor.toHexString(),
    opacity: copyColor.getAlpha(),
  };
};

// 转换省份名称方法
export const transformProvinceName = (name?: string) => {
  switch (name) {
    case "新疆":
      return "新疆维吾尔自治区";
    case "香港":
      return "香港特别行政区";
    case "澳门":
      return "澳门特别行政区";
    case "西藏":
      return "西藏自治区";
    default:
      return name;
  }
};
