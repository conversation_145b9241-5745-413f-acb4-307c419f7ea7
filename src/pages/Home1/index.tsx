import Statistics from "./components/Statistics";
import RealTimeEvents from "./components/RealTimeEvents";
import TypeRanking from "./components/TypeRanking";
import NotificationFrequency from "./components/NotificationFrequency";
import FeedbackRateRanking from "./components/FeedbackRateRanking";
import EventNotification from "./components/EventNotification";
import BgImage from "@/components/BgImage";
import bg from "@/assets/1/bg.webp";

function Home1() {
  return (
    <BgImage url={bg} className="absolute inset-x-0 inset-y-0">
      <Statistics />
      {/* 应急指挥实时事件 */}
      <RealTimeEvents />
      {/* 应急指挥事件类型排行 */}
      <TypeRanking />
      {/* 通报次数Top5 */}
      <NotificationFrequency />
      {/* 地市反馈率排行 */}
      <FeedbackRateRanking />
      {/* 事件通报 */}
      <EventNotification />
    </BgImage>
  );
}
export default Home1;
