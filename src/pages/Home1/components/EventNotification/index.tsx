import bg8 from "@/assets/1/bg8.webp";
import bg9 from "@/assets/1/bg9.png";
import icon1 from "@/assets/1/4.png";
import icon2 from "@/assets/1/5.png";
import BgImage from "@/components/BgImage";
import Arrow from "./components/Arrow";
import ListItem from "./components/ListItem";
import CurvedText from "@/components/CurvedText";

function EventNotification() {
  // 处理数据
  const handleData = [
    {
      name: "广东省网信办值班账号已发布",
      time: "2024-05-30 10:26:03",
    },
    {
      name: "梁展明已查阅",
      time: "2024-05-30 10:46:03",
    },
    {
      name: "梁展明已处置",
      time: "2024-05-30 18:46:03",
    },
    {
      name: "梁展明已处置",
      time: "2024-05-30 18:46:03",
    },
    {
      name: "广东省网信办值班账号结果审核通过",
      time: "2024-05-30 19:46:03",
    },
  ];
  return (
    <div className="absolute left-[530px] right-[520px] bottom-[70px] top-[160px]">
      <img
        src={icon1}
        className="w-[583px] x-centered mt-1 object-cover"
        alt=""
      />
      <div className="text-[#C7CFED] x-centered h-[46px] flex mt-[96px] w-full z-10">
        <div className="bg-[#2B4381] w-[2px]"></div>
        <div className="ml-[2px] w-[42px] bg-[#2B4381] flex items-center justify-center">
          <img src={icon2} className="w-[22px] object-cover" alt="" />
        </div>
        <div
          className="ml-[3px] flex-1 items-center flex px-5 overflow-hidden"
          style={{
            background:
              "linear-gradient(90deg,rgba(43,67,129,1),rgba(43, 67, 129, 0))",
          }}
        >
          <div className="border-transparent border-l-[#A4D7FF] border-[6px] mr-2"></div>
          <div className="color-text truncate">
            2024-05-23 09:55:50
            广东省网信办值班账号发布：关于防范网站搜索关键词遭恶意利用的风险提示
            发生了信息内容安全事件/其他信息内容安全事件，目标IP：0其他信息内容安全事件，目标IP：0其他信息内容安全事件，目标IP：0.0.0.0
          </div>
        </div>
      </div>
      <BgImage
        className="w-[1072px] h-[700px]  centered mt-[156px] z-10 pointer-events-none"
        url={bg8}
      >
        <div className=" pointer-events-auto">
          <div className="absolute text-[14px] top-[198px] x-centered">
            <CurvedText value="疑似深圳市盛凯而信" />
            <CurvedText value="网络安全事件通报" className="mt-[-78px]" />
          </div>
          {[0, 1, 2, 3, 4, 5].map((val, index) => (
            <Arrow key={val} index={index} />
          ))}
          {handleData.map((val, index) => (
            <ListItem key={index} index={index} item={val} />
          ))}
        </div>
      </BgImage>
      <div className="centered -mt-[150px] z-20 pointer-events-none">
        <BgImage
          className="w-[390px] h-[432px] animate-[moveUpDown_2s_linear_infinite]"
          url={bg9}
        />
      </div>
    </div>
  );
}
export default EventNotification;
