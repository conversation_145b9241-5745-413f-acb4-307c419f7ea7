import { motion } from "framer-motion";
import arrow from "@/assets/1/arrow.png";
import { useMemo } from "react";

interface IProps {
  index: number;
}
function Arrow(props: IProps) {
  const { index } = props;

  const translateX = useMemo(() => {
    switch (index) {
      case 0:
        return -12;
      case 1:
        return 14;
      case 2:
        return 24;
      case 3:
        return 30;
      case 4:
        return 8;
      case 5:
        return -5;
      default:
        return 0;
    }
  }, [index]);

  const translateY = useMemo(() => {
    switch (index) {
      case 0:
        return 12;
      case 1:
        return 12;
      case 2:
        return 12;
      case 3:
        return -5;
      case 4:
        return -6;
      case 5:
        return -10;
      default:
        return 0;
    }
  }, [index]);

  const rotateValue = useMemo(() => {
    switch (index) {
      case 0:
        return 120;
      case 1:
        return 60;
      case 2:
        return 15;
      case 3:
        return -15;
      case 4:
        return -60;
      case 5:
        return -120;
      default:
        return 0;
    }
  }, [index]);

  const initial = useMemo(() => {
    switch (index) {
      case 0:
        return {
          top: 256,
          left: 173,
        };
      case 1:
        return {
          top: 396,
          left: 172,
        };
      case 2:
        return {
          top: 490,
          left: 356,
        };
      case 3:
        return {
          top: 504,
          left: 646,
        };
      case 4:
        return {
          top: 412,
          left: 848,
        };

      case 5:
        return {
          top: 280,
          left: 888,
        };

      default:
        return {
          top: 0,
          left: 0,
        };
    }
  }, [index]);

  return (
    <motion.img
      src={arrow}
      animate={{
        opacity: [0, 1, 0],
        translateX: [0, translateX],
        translateY: [0, translateY],
        scale: 0.9,
      }}
      transition={{
        times: [0, 0.9, 1],
        duration: 1.2,
        ease: "linear",
        repeat: Infinity,
        delay: 0,
        repeatDelay: 0.5,
      }}
      initial={{
        rotate: rotateValue,
        scale: 0.8,
        position: "absolute",
        width: 36,
        height: 36,
        ...initial,
      }}
    ></motion.img>
  );
}
export default Arrow;
