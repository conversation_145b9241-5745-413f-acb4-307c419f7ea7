import icon1 from "@/assets/1/icon1.png";
import bg from "@/assets/1/bg10.png";
import BgImage from "@/components/BgImage";
import { useMemo } from "react";

interface IProps {
  index: number;

  item: {
    name: string;
    time: string;
  };
}
function ListItem(props: IProps) {
  const {
    index,
    item: { name, time },
  } = props;

  const styleValue: React.CSSProperties = useMemo(() => {
    switch (index) {
      case 0:
        return {
          left: 106,
          top: 20,
        };

      case 1:
        return {
          left: 176,
          top: 248,
        };

      case 2:
        return {
          left: 425,
          top: 310,
        };

      case 3:
        return {
          right: 172,
          top: 248,
        };

      case 4: {
        return {
          right: 106,
          top: 20,
        };
      }

      default:
        return {};
    }
  }, [index]);

  return (
    <div
      className="w-[220px] h-[172px] flex flex-col items-center absolute animate-[moveUpDown_2s_linear_infinite]"
      style={styleValue}
    >
      <BgImage
        url={bg}
        className="w-[220px] h-[74px] flex flex-col items-center justify-center px-4"
      >
        <div
          className="text-[18px] color-text font-bold truncate w-full text-center"
          title={name}
        >
          {name}
        </div>
        <div className="text-[14px] color-secondary">{time}</div>
      </BgImage>
      <BgImage url={icon1} className="w-[92px] h-[100px]" />
    </div>
  );
}
export default ListItem;
