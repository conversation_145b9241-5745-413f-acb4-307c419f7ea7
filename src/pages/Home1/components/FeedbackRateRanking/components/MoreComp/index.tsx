import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { useMemo } from "react";

interface IProps extends CustomModalProps {
  tabType: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, tabType } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      title: tabType,
      dataIndex: "name",
    },
    {
      dataIndex: "count1",
      title: "反馈事件数量",
    },
    {
      dataIndex: "count2",
      title: "通报事件数量",
    },
    {
      dataIndex: "value",
      title: "反馈率（%）",
    },
  ];

  const data = useMemo(() => {
    if (tabType === "行业") {
      return [
        {
          name: "广播电视",
          value: 100,
          count1: 2,
          count2: 2,
        },
        {
          name: "公路水路运输",
          value: 100,
          count1: 1,
          count2: 1,
        },
        {
          name: "政府部门",
          value: 71.4,
          count1: 5,
          count2: 7,
        },
        {
          name: "其它",
          value: 60,
          count1: 3,
          count2: 5,
        },
      ];
    }
    return [
      {
        name: "广州市",
        value: 2,
        count1: 98,
        count2: 100,
      },
      {
        name: "深圳市",
        value: 90,
        count1: 90,
        count2: 100,
      },
      {
        name: "佛山市",
        value: 84,
        count1: 84,
        count2: 100,
      },
      {
        name: "珠海市",
        value: 74,
        count1: 74,
        count2: 100,
      },
      {
        name: "汕头市",
        value: 68,
        count1: 68,
        count2: 100,
      },
      {
        name: "韶关市",
        value: 64,
        count1: 64,
        count2: 100,
      },
      {
        name: "河源市",
        value: 60,
        count1: 60,
        count2: 100,
      },
      {
        name: "梅州市",
        value: 54,
        count1: 54,
        count2: 100,
      },
      {
        name: "惠州市",
        value: 50,
        count1: 50,
        count2: 100,
      },
      {
        name: "汕尾市",
        value: 48,
        count1: 48,
        count2: 100,
      },
      {
        name: "东莞市",
        value: 40,
        count1: 40,
        count2: 100,
      },
    ];
  }, [tabType]);

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const { page, size } = params;
    const startIndex = (page - 1) * size;
    const endIndex = page * size;
    const paginatedData = data.slice(startIndex, endIndex);
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          data: paginatedData,
          total: data.length,
        });
      }, 300);
    });
  };

  return (
    <ProModal title="反馈率/及时反馈率地市列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 400,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
