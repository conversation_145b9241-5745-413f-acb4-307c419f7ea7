import Card from "@/components/Card";
import { useMemo, useState } from "react";
import useVisible from "@/hooks/useVisible";
import MoreComp from "./components/MoreComp";
import List from "./components/List";

function FeedbackRateRanking() {
  const [activeKey, setActiveKey] = useState("地市");

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const data1 = useMemo(() => {
    return [
      {
        name: "广州市",
        value: 98,
      },
      {
        name: "深圳市",
        value: 90,
      },
      {
        name: "佛山市",
        value: 84,
      },
      {
        name: "珠海市",
        value: 74,
      },
      {
        name: "汕头市",
        value: 68,
      },
      {
        name: "韶关市",
        value: 64,
      },
      {
        name: "河源市",
        value: 60,
      },
      {
        name: "梅州市",
        value: 54,
      },
      {
        name: "惠州市",
        value: 50,
      },
      {
        name: "汕尾市",
        value: 48,
      },
      {
        name: "东莞市",
        value: 40,
      },
    ];
  }, []);

  const data2 = useMemo(() => {
    return [
      {
        name: "广播电视",
        value: 100,
      },
      {
        name: "公路水路运输",
        value: 100,
      },
      {
        name: "政府部门",
        value: 71.4,
      },
      {
        name: "其它",
        value: 60,
      },
    ];
  }, []);

  const data = useMemo(() => {
    if (activeKey === "地市") {
      return data1;
    }
    return data2;
  }, [activeKey, data1, data2]);

  return (
    <div className="absolute right-[42px] top-[438px] w-[483px] h-[540px]">
      <MoreComp
        open={open}
        onCancel={setFalse}
        key={visibleKey}
        tabType={activeKey}
      />
      <Card
        headerType={1}
        title="地市反馈率排行"
        tabsProps={{
          items: [
            {
              key: "地市",
              label: "地市",
            },
            {
              key: "行业",
              label: "行业",
            },
          ],
          activeKey,
          onChange: setActiveKey,
        }}
        onMore={setTrue}
      >
        <List data={data} key={activeKey} tabType={activeKey} />
      </Card>
    </div>
  );
}
export default FeedbackRateRanking;
