import type { LinearConfig } from "@/typings";
import { formatLinear } from "@/utils";
import { motion } from "framer-motion";
import icon1 from "@/assets/1/6.png";
import icon2 from "@/assets/1/7.png";
import { useMemo } from "react";

interface IProps {
  index: number;

  rowGap: number;

  itemHeight: number;

  item: {
    value: number;

    name: string;
  };

  max: number;

  // 是否动画
  showAnimate?: boolean;
}
function ListItem(props: IProps) {
  const {
    index,
    rowGap,
    itemHeight,
    item: { value, name },
    max,
    showAnimate = true,
  } = props;

  const colorConfig: LinearConfig = useMemo(() => {
    if (index < 3) {
      return {
        angle: 270,
        colorStops: [
          {
            offset: 0,
            color: "rgba(251, 238, 150, 1)",
          },
          {
            offset: 0.25,
            color: "rgba(255, 232, 76, 0.71)",
          },
          {
            offset: 1,
            color: "rgba(218, 192, 16, 0.19)",
          },
        ],
      };
    }
    return {
      angle: 270,
      colorStops: [
        {
          offset: 0,
          color: "rgba(164, 205, 255, 1)",
        },
        {
          offset: 0.25,
          color: "rgba(52, 143, 255, 0.71)",
        },
        {
          offset: 1,
          color: "rgba(52, 143, 255, 0.19)",
        },
      ],
    };
  }, [index]);

  const barWidth = useMemo(() => {
    return `${(value / max) * 100}%`;
  }, [max, value]);

  const animate = useMemo(() => {
    return {
      width: barWidth,
      transition: {
        duration: showAnimate ? 1 : 0,
      },
    };
  }, [barWidth, showAnimate]);

  const imgAnimate = useMemo(() => {
    return {
      left: `calc(${barWidth} - 9px)`,
      transition: {
        duration: showAnimate ? 1 : 0,
      },
    };
  }, [barWidth, showAnimate]);

  return (
    <div
      key={`item${index}`}
      className="flex items-center gap-x-3 color-text"
      style={{
        marginBottom: rowGap,
        height: itemHeight,
      }}
    >
      <div className="w-[66px] h-[26px] bg-[rgba(58,71,92,0.68)] flex items-center justify-center">
        Top {index + 1 < 10 ? 0 : ""}
        {index + 1}
      </div>
      <div className="flex-1 flex items-center flex-col gap-y-1">
        <div className="flex items-center justify-between w-full -mt-[6px]">
          <div className="flex-1">{name}</div>
          <div
            className="text-[18px]"
            style={{
              fontFamily: "DINCond-Bold",
            }}
          >
            {value}
          </div>
        </div>
        <div className="w-full h-[4px] flex items-center bg-[#232E40] relative">
          <motion.div
            className="h-full"
            style={{
              background: formatLinear(colorConfig),
            }}
            animate={animate}
          ></motion.div>
          <motion.img
            className="absolute size-[52px] -translate-y-1/2 top-1/2 ml-[-16px] mt-[-1px]"
            src={index < 2 ? icon1 : icon2}
            animate={imgAnimate}
            alt=""
          />
        </div>
      </div>
    </div>
  );
}
export default ListItem;
