import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { useMemo } from "react";

interface IProps extends CustomModalProps {
  tabType: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, tabType } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "name",
      title: tabType,
    },
    {
      dataIndex: "value",
      title: "通报次数",
    },
  ];

  const data = useMemo(() => {
    if (tabType === "行业") {
      return [
        {
          name: "政府部门",
          value: 7,
        },
        {
          name: "其他",
          value: 5,
        },
        {
          name: "广播电视",
          value: 3,
        },
        {
          name: "公路水路运输",
          value: 2,
        },
        {
          name: "卫生健康",
          value: 1,
        },
      ];
    }
    return [
      {
        name: "广州市",
        value: 18,
      },
      {
        name: "深圳市",
        value: 16,
      },
      {
        name: "佛山市",
        value: 15,
      },
      {
        name: "珠海市",
        value: 14,
      },
      {
        name: "汕头市",
        value: 13,
      },
      {
        name: "韶关市",
        value: 10,
      },
      {
        name: "河源市",
        value: 8,
      },
      {
        name: "梅州市",
        value: 8,
      },
      {
        name: "惠州市",
        value: 6,
      },
      {
        name: "汕尾市",
        value: 6,
      },
      {
        name: "东莞市",
        value: 6,
      },
      {
        name: "中山市",
        value: 6,
      },
      {
        name: "湛江市",
        value: 6,
      },
    ];
  }, [tabType]);

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const { page, size } = params;
    const startIndex = (page - 1) * size;
    const endIndex = page * size;
    const paginatedData = data.slice(startIndex, endIndex);
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          data: paginatedData,
          total: data.length,
        });
      }, 300);
    });
  };

  return (
    <ProModal title="地市通报次数列" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
