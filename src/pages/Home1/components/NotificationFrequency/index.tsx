import Card from "@/components/Card";
import { useMemo, useState } from "react";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import List from "./components/List";

function NotificationFrequency() {
  const data1 = useMemo(() => {
    return [
      {
        name: "广州市",
        value: 18,
      },
      {
        name: "深圳市",
        value: 16,
      },
      {
        name: "佛山市",
        value: 15,
      },
      {
        name: "珠海市",
        value: 14,
      },
      {
        name: "汕头市",
        value: 13,
      },
      {
        name: "韶关市",
        value: 10,
      },
      {
        name: "河源市",
        value: 8,
      },
      {
        name: "梅州市",
        value: 8,
      },
      {
        name: "惠州市",
        value: 6,
      },
      {
        name: "汕尾市",
        value: 6,
      },
      {
        name: "东莞市",
        value: 6,
      },
    ];
  }, []);

  const data2 = useMemo(() => {
    return [
      {
        name: "政府部门",
        value: 7,
      },
      {
        name: "其他",
        value: 5,
      },
      {
        name: "广播电视",
        value: 3,
      },
      {
        name: "公路水路运输",
        value: 2,
      },
      {
        name: "卫生健康",
        value: 1,
      },
    ];
  }, []);

  const [activeKey, setActiveKey] = useState("地市");

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const data = useMemo(() => {
    if (activeKey === "地市") {
      return data1;
    }
    return data2;
  }, [activeKey, data1, data2]);

  return (
    <div className="absolute right-[42px] top-[130px] w-[483px]">
      <Card
        headerType={1}
        title="通报次数Top5"
        tabsProps={{
          items: [
            {
              key: "地市",
              label: "地市",
            },
            {
              key: "行业",
              label: "行业",
            },
          ],
          activeKey,
          onChange: setActiveKey,
        }}
        onMore={setTrue}
      >
        <List data={data} key={activeKey} tabType={activeKey} />
      </Card>
      <MoreComp
        open={open}
        onCancel={setFalse}
        key={visibleKey}
        tabType={activeKey}
      />
    </div>
  );
}
export default NotificationFrequency;
