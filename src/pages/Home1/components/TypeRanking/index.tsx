import Card from "@/components/Card";
import bg6 from "@/assets/1/bg6.png";
import bg7 from "@/assets/1/bg7.png";
import BgImage from "@/components/BgImage";
import { nanoid } from "nanoid";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import React, { useCallback, useMemo, useRef } from "react";
import SvgLinearGradient from "@/components/Comp/SvgLinearGradient";
import type { LinearConfig } from "@/typings";
import { formatLinear } from "@/utils";

function TypeRanking() {
  const data = [
    { name: "网络攻击事件", value: 5 },
    { name: "其他事件", value: 4 },
    { name: "有害程序事件", value: 4 },
    { name: "信息内容安全事件", value: 1 },
    { name: "信息破坏事件", value: 1 },
  ];

  const colorList: LinearConfig[] = useMemo(
    () => [
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(166, 207, 255, 1)",
          },
          {
            offset: 1,
            color: "rgba(166, 207, 255, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(44, 81, 250, 1)",
          },
          {
            offset: 1,
            color: "rgba(44, 81, 250, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(211,173,247,1)",
          },
          {
            offset: 1,
            color: "rgba(211,173,247,0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(238, 111, 124, 1)",
          },
          {
            offset: 1,
            color: "rgba(238, 111, 124, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(255, 207, 95, 1)",
          },
          {
            offset: 1,
            color: "rgba(255, 207, 95, .75)",
          },
        ],
      },
    ],
    []
  );

  const linearIdRef = useRef(`${nanoid()}_linear_`);

  const getColor = useCallback(
    (index) => {
      return colorList[index];
    },
    [colorList]
  );

  return (
    <div className="absolute left-[40px] top-[740px] w-[483px] h-[240px]">
      <Card headerType={1} title="应急指挥事件类型排行">
        <div className="flex mt-2">
          <div className="size-[228px] relative flex justify-center items-center">
            <div className="centered">
              <BgImage
                url={bg6}
                className="w-[228px] h-[220px] animate-[rotateCounterClockwise_16s_linear_infinite]"
              />
            </div>
            <div className="centered">
              <BgImage
                url={bg7}
                className="size-[112px] animate-[rotateClockwise_16s_linear_infinite]"
              ></BgImage>
            </div>
            <PieChart
              width={190}
              height={190}
              margin={{
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
              }}
            >
              {colorList.map((_, index) => {
                const linearId = `${linearIdRef.current}${index}`;
                const colorConfig = getColor(index);
                return (
                  <React.Fragment key={linearId}>
                    <defs>
                      <SvgLinearGradient id={linearId} config={colorConfig} />
                    </defs>
                  </React.Fragment>
                );
              })}
              <Pie
                data={data}
                cx={95}
                cy={95}
                innerRadius={76}
                outerRadius={90}
                paddingAngle={6} // 饼图内间距
                dataKey="value"
                stroke="none"
                cornerRadius={0} // 圆角
                isAnimationActive={false}
              >
                {data.map((entry, index) => {
                  const linearId = `${linearIdRef.current}${index}`;
                  return (
                    <React.Fragment key={linearId}>
                      <Cell
                        style={{
                          outline: "none",
                          overflow: "hidden",
                        }}
                        key={`cell-${index}`}
                        fill={`url(#${linearId})`}
                      />
                    </React.Fragment>
                  );
                })}
              </Pie>
            </PieChart>
          </div>
          <div className="flex flex-col flex-1 gap-y-3 self-center ml-6">
            {data.map((val, index) => {
              const colorConfig = getColor(index);
              return (
                <div
                  key={`legend${index + 1}`}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center">
                    <span
                      className="size-[10px] rounded-full inline-block"
                      style={{
                        background: formatLinear(colorConfig),
                      }}
                    ></span>
                    <span className="ml-4 color-text text-[17px]">
                      {val.name}
                    </span>
                  </div>
                  <div
                    className="ml text-[20px]"
                    style={{
                      backgroundImage: formatLinear(colorConfig),
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      // fontFamily: "YouSheBiaoTiHei-2",
                    }}
                  >
                    {val.value}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Card>
    </div>
  );
}
export default TypeRanking;
