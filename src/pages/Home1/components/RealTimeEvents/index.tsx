import Card from "@/components/Card";
import List from "@/components/List";
import useVisible from "@/hooks/useVisible";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";

function RealTimeEvents() {
  const dataSource = [
    {
      name: "关于防范网站搜索关键词遭恶意利用的风险提示",
      time: "2024-06-20 16:38:02",
      status: "待终审",
    },
    {
      name: "潮州市潮安区疾病预防控制中心",
      time: "2024-06-20 16:38:02",
      status: "待终审",
    },
    {
      name: "汕头市储备粮食和物资有限公司",
      time: "2024-06-20 16:38:02",
      status: "待终审",
    },
    {
      name: "优居集团二手房客户交易数据泄露",
      time: "2024-06-20 16:38:02",
      status: "已完结",
    },
    {
      name: "通天星视频监控平台漏洞风险处置事",
      time: "2024-06-20 16:38:02",
      status: "已完结",
    },
    {
      name: "广东省佛山市某企业办公系统遭反共黑客篡改",
      time: "2024-06-20 16:38:02",
      status: "已完结",
    },
    {
      name: "佛山市南海区教育局所属主机感染僵尸木马",
      time: "2024-06-20 16:38:02",
      status: "已完结",
    },
    {
      name: "广州公务用车管理平台所属资产与境外IP通联-114",
      time: "2024-06-20 16:38:02",
      status: "已完结",
    },
  ];

  const columns: TableProps["columns"] = [
    {
      title: "序号",
      key: "dataIndex",
      render: (_, __, index) => index + 1,
      width: 50,
      align: "center",
    },
    {
      title: "单位名称",
      dataIndex: "name",
      key: "name",
      width: 150,
    },
    {
      title: "事件发生事件",
      dataIndex: "time",
      key: "time",
      width: 140,
    },
    {
      title: "当前状态",
      dataIndex: "status",
      key: "status",
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className=" absolute left-[40px] top-[438px] w-[483px] h-[240px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="应急指挥实时事件" onMore={setTrue}>
        <List
          width={452}
          height={238}
          data={dataSource}
          size={5}
          columns={columns}
        />
      </Card>
    </div>
  );
}
export default RealTimeEvents;
