import ionBg1 from "@/assets/1/bg1.png";
import iconBg2 from "@/assets/1/bg2.png";
import iconBg3 from "@/assets/1/bg3.png";
import iconBg4 from "@/assets/1/bg4.png";
import iconBg5 from "@/assets/1/bg5.png";
import icon1 from "@/assets/1/1.png";
import icon2 from "@/assets/1/2.png";
import icon3 from "@/assets/1/3.png";

import React from "react";

function Statistics() {
  return (
    <React.Fragment>
      <div
        className="absolute left-[58px] top-[130px] w-[196px] h-[175px] bg-cover text-center"
        style={{
          backgroundImage: `url(${ionBg1})`,
        }}
      >
        <img
          className="inline-block w-[100px] animate-[moveOpacity_2s_ease-in-out_infinite]"
          src={icon1}
          alt=""
        />
      </div>
      <div
        className="absolute left-[283px] top-[144px] w-[222px] h-[74px] flex items-center justify-center flex-col"
        style={{
          backgroundImage: `url(${iconBg2})`,
        }}
      >
        <div className="color-secondary">通报事件总数</div>
        <div className="text-[32px] leading-[36px] color-text font-[DINCond-Bold]">
          36
        </div>
      </div>
      <div
        className="absolute left-[283px] top-[224px] w-[222px] h-[74px] flex items-center justify-center flex-col"
        style={{
          backgroundImage: `url(${iconBg3})`,
        }}
      >
        <div className="text-[rgba(255,199,101,0.65)]">重大事件</div>
        <div className="text-[32px] leading-[36px] text-[rgba(255,199,101,0.85)]">
          1
        </div>
      </div>
      <div className="absolute left-[65px] top-[322px] flex">
        <div
          className="w-[106px] h-[106px] flex items-center justify-center"
          style={{
            backgroundImage: `url(${iconBg4})`,
          }}
        >
          <img src={icon2} alt="" className="w-[94px] -mt-2" />
        </div>
        <div
          className="w-[114px] h-[106px] text-center"
          style={{
            backgroundImage: `url(${iconBg5})`,
          }}
        >
          <div className="color-secondary leading-[40px]">反馈率</div>
          <div className="color-text leading-[56px] text-[32px] font-[DINCond-Bold]">
            95%
          </div>
        </div>
      </div>
      <div className="absolute left-[297px] top-[322px] flex">
        <div
          className="w-[106px] h-[106px] flex items-center justify-center"
          style={{
            backgroundImage: `url(${iconBg4})`,
          }}
        >
          <img src={icon3} alt="" className="w-[94px] -mt-2" />
        </div>
        <div
          className="w-[114px] h-[106px] text-center"
          style={{
            backgroundImage: `url(${iconBg5})`,
          }}
        >
          <div className="color-secondary leading-[40px]">归档率</div>
          <div className="color-text leading-[56px] text-[32px] font-[DINCond-Bold]">
            60%
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}
export default Statistics;
