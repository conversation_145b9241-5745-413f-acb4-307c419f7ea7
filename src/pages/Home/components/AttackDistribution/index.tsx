import Card from "@/components/Card";
import List from "@/components/List";
import useVisible from "@/hooks/useVisible";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import { useState } from "react";

function AttackDistribution() {
  const dataSource = [
    {
      name: "中国",
      count: 1700,
      ratio: 180.1,
    },
    {
      name: "浙江",
      count: 8,
      ratio: 75,
    },
    {
      name: "广东",
      count: 7,
      ratio: -73.1,
    },
    {
      name: "云南",
      count: 2,
      ratio: 95.9,
    },
    {
      name: "上海",
      count: 1,
      ratio: -96.2,
    },
    {
      name: "北京",
      count: 1,
      ratio: 92.2,
    },
    {
      name: "四川",
      count: 1,
      ratio: 92.2,
    },
    {
      name: "江苏",
      count: 1,
      ratio: 88.6,
    },
    {
      name: "安徽",
      count: 1,
      ratio: 66.2,
    },
  ];

  const [activeKey, setActiveKey] = useState("境内");

  const columns: TableProps["columns"] = [
    {
      title: "序号",
      key: "dataIndex",
      dataIndex: "dataIndex",
      render: (_, __, index) => index + 1,
      width: 60,
      align: "center",
      className: "mr-4",
    },
    {
      title: "国家/地区",
      dataIndex: "name",
      key: "name",
      width: 140,
    },
    {
      title: "事件数量",
      dataIndex: "count",
      key: "count",
    },
    {
      title: "环比",
      dataIndex: "ratio",
      key: "ratio",
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className="absolute right-[44px] top-[136px] w-[483px] h-[240px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card
        headerType={1}
        title="攻击源分布"
        onMore={setTrue}
        tabsProps={{
          items: [
            {
              key: "境内",
              label: "境内",
            },
            {
              key: "境外",
              label: "境外",
            },
          ],
          activeKey,
          onChange: setActiveKey,
        }}
      >
        <div className="mt-1">
          <List
            width={452}
            height={238}
            data={dataSource}
            size={5}
            columns={columns}
          />
        </div>
      </Card>
    </div>
  );
}
export default AttackDistribution;
