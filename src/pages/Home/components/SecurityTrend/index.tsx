import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import type { Coloumns } from "@/components/BasicLine";
import BasicLine from "@/components/BasicLine";
import { useMemo } from "react";
// import MoreComp from "./components/MoreComp";

function SecurityTrend() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const data = [
    {
      name: "20240411",
      value: 60,
    },
    {
      name: "20240416",
      value: 40,
    },
    {
      name: "20240419",
      value: 40,
    },
    {
      name: "20240423",
      value: 70,
    },
    {
      name: "20240426",
      value: 70,
    },
    {
      name: "20240428",
      value: 50,
    },
    {
      name: "20240429",
      value: 50,
    },
    {
      name: "20240430",
      value: 40,
    },
    {
      name: "20240431",
      value: 40,
    },
    {
      name: "20240436",
      value: 45,
    },
    {
      name: "20240435",
      value: 45,
    },
    {
      name: "20240434",
      value: 30,
    },
    {
      name: "20240433",
      value: 60,
    },
    {
      name: "20240432",
      value: 80,
    },
  ];

  const coloumns = useMemo<Coloumns>(() => {
    return [
      {
        dataKey: "value",
        color: "#F3B67B",
        name: "安全指数",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(243, 182, 123, 0.4)",
            },
            {
              offset: 1,
              color: "rgba(243, 182, 123, 0)",
            },
          ],
        },
      },
    ];
  }, []);

  return (
    <div className="absolute left-[40px] top-[436px] w-[483px]">
      {/* <MoreComp open={open} onCancel={setFalse} key={visibleKey} /> */}
      <Card headerType={1} title="安全趋势分析" onMore={setTrue}>
        <BasicLine data={data} width={470} height={240} coloumns={coloumns} />
      </Card>
    </div>
  );
}
export default SecurityTrend;
