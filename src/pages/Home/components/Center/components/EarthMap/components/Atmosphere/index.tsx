import { use<PERSON>rame, useThree } from "@react-three/fiber";
import React, { useMemo } from "react";

import * as THREE from "three";
import { THREE_EARTH_POSITION_Z, THREE_EARTH_RADIUS } from "../../config";

function Atmosphere() {
  const radius = THREE_EARTH_RADIUS;

  const levels = 1;

  const glowHeight = useMemo(() => {
    // 暂时未找到规律 这样延缓变化浮动误差
    if (levels > 10) {
      return (100 - levels) / 10;
    }
    return 50 - levels * 4;
  }, [levels]);

  const meshRef = React.useRef<THREE.Mesh>(null);
  const { camera } = useThree();

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.lookAt(camera.position);
    }
  });

  const vertexShader = `
      varying vec3 vertexNormal;
      uniform float positionValue;
      void main() {
      vertexNormal = normal;
      float cameraDistance = positionValue / length(cameraPosition); // Camera scale, original positionValue/current

      float scale = cameraDistance;
      if (cameraDistance < 0.4) {
          scale = (1.0 - cameraDistance) / 2.0 + cameraDistance;
      } else if (cameraDistance < 0.5) {
          scale = (1.0 - cameraDistance) / 2.25 + cameraDistance;
      } else if (cameraDistance < 0.65) {
          scale = (1.0 - cameraDistance) / 2.5 + cameraDistance;
      } else if (cameraDistance < 0.85) {
          scale = (1.0 - cameraDistance) / 3.0 + cameraDistance;
      } else if (cameraDistance < 1.0) {
          scale = (1.0 - cameraDistance) / 5.0 + cameraDistance;
      }
      vec4 modelViewPosition = modelViewMatrix * vec4(position * scale, 1.0);
      gl_Position = projectionMatrix * modelViewPosition;
  }
  `;

  const fragmentShader = `
    uniform vec3 color;
    varying vec3 vertexNormal;
    uniform float glowHeight; // 扩散的高度
    void main() {
      float rim = 0.2 - dot(vertexNormal, vec3(0., 0., 1.));
      rim = pow(rim, glowHeight); // 根据 glowHeight 动态调整发光范围
      //  *2.0 发光的强度
      gl_FragColor = vec4(color * 1.0,0.15) * rim;
    }
  `;

  return (
    <group>
      <mesh>
        <sphereGeometry args={[radius, 64, 64]} />
        <meshBasicMaterial side={THREE.BackSide} transparent opacity={0.0} />
      </mesh>
      <mesh ref={meshRef} renderOrder={10}>
        <sphereGeometry args={[radius * 2.8, 64, 64]} />
        <shaderMaterial
          uniforms={{
            color: { value: new THREE.Color("#a4c8f5") },
            glowHeight: {
              value: glowHeight,
            },
            positionValue: {
              value: THREE_EARTH_POSITION_Z,
            },
          }}
          vertexColors
          side={THREE.BackSide}
          transparent
          vertexShader={vertexShader}
          fragmentShader={fragmentShader}
          blending={THREE.AdditiveBlending}
        />
      </mesh>
    </group>
  );
}
export default Atmosphere;
