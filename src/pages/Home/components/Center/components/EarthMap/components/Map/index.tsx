import React from "react";
import * as THREE from "three";
import { useTexture } from "@react-three/drei";
import earthMap from "@/assets/0/earthMap.jpg";
import emissiveMap from "@/assets/0/emissiveMap.png";
import earthClouds from "@/assets/0/earthClouds.jpg";
import { THREE_EARTH_RADIUS } from "../../config";
import Atmosphere from "../Atmosphere";
import SwitchScene from "../SwitchScene";

function Map() {
  const radius = THREE_EARTH_RADIUS;

  const [basicTexture, emissiveTexture, cloudsTexture] = useTexture([
    earthMap,
    emissiveMap,
    earthClouds,
  ]);

  return (
    <React.Fragment>
      <group>
        {/* 云层 */}
        <mesh>
          <sphereGeometry args={[radius - 1, 64, 64]} />
          <meshPhongMaterial color="#fff" opacity={0.0} transparent />
        </mesh>
        <mesh renderOrder={20}>
          <sphereGeometry args={[radius + 10, 64, 64]} />
          <meshStandardMaterial
            color={new THREE.Color("#fff")}
            opacity={0.4}
            alphaMap={cloudsTexture}
            depthWrite
            transparent
          />
        </mesh>
      </group>

      <Atmosphere />

      <group>
        <mesh>
          {/* 使用球体几何体创建地球，参数分别为半径、水平和垂直的分段数 */}
          <sphereGeometry args={[radius, 64, 64]} />
          <meshStandardMaterial
            map={basicTexture}
            emissiveMap={emissiveTexture}
            emissiveIntensity={4}
            emissive={new THREE.Color("#fff")}
          />
        </mesh>
      </group>

      <SwitchScene />
    </React.Fragment>
  );
}
export default Map;
