import { createContainer } from "unstated-next";
import chinaJson from "./100000.json";
import { ProjectionTypeEnum } from "@/enum";
import useMapData from "./hooks/useMapData";
import useShape from "./hooks/useShape";
import mapBgUrl from "@/assets/0/chinaTexture.png";
import type * as THREE from "three";
import { useRef } from "react";

function useContainer() {
  const projectionType = ProjectionTypeEnum.Mercator;

  const depth = 6;

  const subGroupRef = useRef<THREE.Group>(null);

  const {
    featureData,
    mapPosition,
    handleCalcUv2,
    geoProjection,
    getProvinceCenter,
  } = useMapData({
    projectionType,
    dataJson: JSON.stringify(chinaJson),
    depth,
  });

  const mapBgMargin = {
    left: 0,
    right: 0,
    top: 0,
    bottom: -0.02,
  };

  const { meshMaterial, sideMaterial } = useShape({
    mapBgUrl,
    mapBgMargin,
  });

  return {
    featureData,
    mapPosition,
    meshMaterial,
    handleCalcUv2,
    sideMaterial,
    depth,
    subGroupRef,
    geoProjection,
    getProvinceCenter,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
