import React, { useEffect, useMemo, useRef } from "react";

import {
  BufferGeometry,
  Color,
  Float32BufferAttribute,
  ShaderMaterial,
  AdditiveBlending,
} from "three";
import type * as THREE from "three";
import gsap from "gsap";
import type { CoordinateItem } from "@/typings";
import useCommon from "../../hooks/useCommon";

interface IProps {
  item: CoordinateItem;
}

function RenderFlowLine(props: IProps) {
  const { item } = props;

  const { getCurvePoint } = useCommon();

  const styleConfig = useMemo(() => {
    return {
      flowLine: {
        color: {
          linear: {
            angle: 0,
            colorStops: [
              { color: "rgba(246,103,8,0.12)", offset: 0 },
              { color: "rgba(255,255,229,1)", offset: 100 },
            ],
            opacity: 1,
          },
          type: "pure",
          pure: "#FFFFFF",
        },
        length: 0.5,
      },
      width: 0.35,
      blending: true,
      runwayLine: { color: "rgba(197,130,36,0.3)", show: true },
      transparent: true,
      height: 45,
    };
  }, []);

  const { flowLine, width } = styleConfig;

  const pointsRef = useRef<THREE.Points>(null);

  const animationConfig = { duration: 3, random: false, interval: 0 };

  // 获取当前线的坐标点信息
  const points = useMemo(() => {
    return getCurvePoint(item);
  }, [getCurvePoint, item]);

  // 创建物体
  const geometry = useMemo(() => {
    const indexList = points.map((_, index) => index);
    // 根据坐标点数组创建出一个线状几何体
    const bufferGeometry = new BufferGeometry().setFromPoints(points);
    // 给几何体添加自定义的索引标识 用来后续根据索引设置点的透明度
    bufferGeometry.setAttribute(
      "aIndex",
      new Float32BufferAttribute(indexList, 1)
    );
    return bufferGeometry;
  }, [points]);

  const material = useMemo(() => {
    // 起点颜色
    let color1 = "#FFC107";
    // 终点颜色
    let color2 = "#FFFFFF";
    // TODO: 暂时只支持两个节点的渐变 多渐变节点的情况需要优化
    // if (flowLine.color.type === ColorTypeEnum.Pure) {
    //   color1 = flowLine.color.pure;
    //   color2 = flowLine.color.pure;
    // } else {
    //   color1 = flowLine.color.linear.colorStops?.at(0)?.color ?? "";
    //   color1 = flowLine.color.linear.colorStops?.at(-1)?.color ?? "";
    // }
    // 创建一个着色器材质
    return new ShaderMaterial({
      depthTest: false,
      uniforms: {
        uColor1: {
          value: new Color(color1),
        },
        uColor2: {
          value: new Color(color2),
        },
        uTime: {
          value: 0,
        },
        uLength: {
          value: points.length,
        },
        uLineLength: {
          value: flowLine.length,
        },
        uWidth: {
          value: width,
        },
      },
      vertexShader: /*glsl*/ `
        attribute float aIndex;
        uniform float uTime;
        uniform float uLength;
        uniform float uLineLength;
        uniform float uWidth;
        varying float vSize;
        varying float vIndex;
        varying float vTime;
        varying float vLength;
        void main() {
          // 获取高度
          vec4 viewPosition = viewMatrix * modelMatrix * vec4(position,1);
          // 传递当前的索引到片面着色器
          vIndex = aIndex;
          // 传递当前的时间到片面着色器 、、 // 片面着色器拿不到外部传入的属性 所以需要顶点着色器传入
          vTime = uTime;
          // 传递总长度到片面着色器
          vLength = uLineLength;
          // 配置定点位置
          gl_Position = projectionMatrix *  viewPosition;
          // 计算当前圆点大小
          vSize = aIndex - uTime * (1.0 + uLineLength);
          if(vSize<=0.0){
              vSize = vSize + uLength * uLineLength;
          }else{
              vSize = 0.0;
          }
          gl_PointSize = 10.0 * uWidth;
        }
      `,
      fragmentShader: /*glsl*/ `
        varying float vIndex;
        varying float vSize;
        varying float vTime;
        varying float vLength;
        uniform vec3 uColor1;
        uniform vec3 uColor2;
        void main(){
          float start = vTime - vLength * 300.0;
          float end = vTime;
          float diff = end - start;
          float ratio = (end - vIndex) / diff;
          vec3 gradient = mix(uColor1, uColor2, ratio);
          if(vSize<=0.0){
              gl_FragColor = vec4(1,0,0,0);
          }else{
              gl_FragColor = vec4(gradient,1);
          }
        }
      `,
      transparent: true,
      vertexColors: false,
    });
  }, [flowLine.length, points.length, width]);

  useEffect(() => {
    if (pointsRef.current) {
      gsap.fromTo(
        (pointsRef?.current?.material as any)?.uniforms?.uTime,
        { value: 0 },
        {
          value: 1000,
          duration: animationConfig.duration,
          repeat: -1,
          delay: animationConfig.interval,
          ease: "none",
        }
      );
    }
  }, [animationConfig.duration, animationConfig.interval, styleConfig]);

  return (
    <React.Fragment>
      <points
        renderOrder={1}
        ref={pointsRef}
        args={[geometry, material]}
      ></points>
    </React.Fragment>
  );
}

export default RenderFlowLine;

