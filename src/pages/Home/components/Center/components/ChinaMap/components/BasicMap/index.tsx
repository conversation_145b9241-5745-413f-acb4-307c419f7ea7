import { memo } from "react";
import RenderShape from "./components/RenderShape";
import React from "react";
import Container from "../../container";
import useEvent from "../../hooks/useEvent";

function BasicMap() {
  const { featureData, mapPosition } = Container.useContainer();

  const { onPointerEnter, onPointerLeave } = useEvent();

  return (
    <group position={mapPosition}>
      {featureData.map((val, index) => {
        const adcode = val.properties?.adcode;
        const name = val.properties?.name;
        return (
          <group
            key={adcode ?? index}
            onPointerEnter={onPointerEnter}
            onPointerLeave={onPointerLeave}
            userData={{
              adcode,
              name,
              index,
            }}
          >
            {/* 遍历每个特征的多边形坐标，并为每个多边形渲染线条 */}
            {val.shapeList.map((item) => {
              return (
                <React.Fragment key={item.key}>
                  {/* 面 */}
                  <RenderShape shape={item.shape} adcode={adcode} name={name} />
                </React.Fragment>
              );
            })}
          </group>
        );
      })}
    </group>
  );
}
export default memo(BasicMap);
