import { useMemo } from "react";
import Container from "../../../../../container";

interface IProps {
  data: {
    src: string;
    dst: string;
  }[];
}
function useCoordinateData(props: IProps) {
  const { data } = props;

  const { geoProjection, getProvinceCenter } = Container.useContainer();

  const coordinateData = useMemo(() => {
    // 未配置地图
    if (!geoProjection) return;
    const source = data.map((item) => {
      // 经纬度转平面坐标
      const [x0Lng, y0Lng] = getProvinceCenter(undefined, item.src) ?? [0, 0];
      const [x1Lng, y1Lng] = getProvinceCenter(undefined, item.dst) ?? [0, 0];

      const [x0, y0] = geoProjection([x0Lng, y0Lng]);
      const [x1, y1] = geoProjection([x1Lng, y1Lng]);
      return {
        x0,
        y0: -y0, // y轴反转
        x1,
        y1: -y1, // y轴反转
      };
    });
    return source;
  }, [data, geoProjection, getProvinceCenter]);

  return {
    coordinateData,
  };
}

export default useCoordinateData;
