import React, { useMemo } from "react";
import {
  CatmullRomCurve3,
  Mesh,
  MeshBasicMaterial,
  TubeGeometry,
} from "three";
import type { CoordinateItem } from "@/typings";
import { transformColor } from "@/utils";
import useCommon from "../../hooks/useCommon";

interface IProps {
  item: CoordinateItem;
}

function RenderRunwayLine(props: IProps) {
  const { item } = props;

  const { getCurvePoint } = useCommon();

  const styleConfig = {
    width: 0.1,
    blending: false,
    runwayLine: { color: "#c76e5d", show: true },
    transparent: true,
    height: 45,
  };

  const { runwayLine, transparent, width } = styleConfig;

  const curve = useMemo(() => {
    const points = getCurvePoint(item);
    return new CatmullRomCurve3(points);
  }, [getCurvePoint, item]);

  const lineMesh = useMemo(() => {
    const lineColor = transformColor(runwayLine.color);
    const tubeGeometry = new TubeGeometry(
      curve, // 一个由基类Curve继承而来的3D路径。
      256, // 组成这一管道的分段数，默认值为64。
      width * 2, // 管道半径。宽度比设置的大一点是为了让流线从管道内部穿过
      8, // 管道横截面的分段数目，默认值为8。
      false // 管道的两端是否闭合。
    );
    const material = new MeshBasicMaterial({
      color: lineColor.value,
      transparent,
      // blending: blending ? AdditiveBlending : undefined,
      opacity: 1,
      depthTest: false, // 不进行深度检测 在鼠标悬浮的时候
    });
    const mesh = new Mesh(tubeGeometry, material);
    return mesh;
  }, [curve, runwayLine.color, transparent, width]);

  return (
    <React.Fragment>
      <primitive object={lineMesh} />
    </React.Fragment>
  );
}

export default RenderRunwayLine;
