import Container from "../../container";
import FlyLine from "./components/FlyLine";
import * as THREE from "three";

function SubComponent() {
  const { depth, subGroupRef, mapPosition } = Container.useContainer();

  const subPostion = new THREE.Vector3(mapPosition.x, mapPosition.y, 0);

  return (
    <group position={[0, 0, depth]} ref={subGroupRef}>
      {/* 飞线 */}
      <group
        position={subPostion}
        userData={{
          needInteractive: true,
        }}
      >
        <FlyLine />
      </group>
    </group>
  );
}
export default SubComponent;
