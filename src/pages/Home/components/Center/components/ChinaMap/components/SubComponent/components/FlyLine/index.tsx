import React from "react";
import RenderRunwayLine from "./components/RenderRunwayLine";
import useCoordinateData from "./hooks/useCoordinateData";
import RenderFlowLine from "./components/RenderFlowLine";

function FlyLine() {
  const dataValue = [
    {
      c_event_log_count: 51,
      src: "福建",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 5927,
      src: "香港",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 188,
      src: "浙江",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 4576,
      src: "中国",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 86,
      src: "台湾",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 278,
      src: "湖南",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 148,
      src: "河南",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 57,
      src: "江苏",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 4,
      src: "四川",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 8,
      src: "澳门",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 8,
      src: "上海",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 54,
      src: "北京",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 1,
      src: "云南",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 1,
      src: "西藏",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 3,
      src: "安徽",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 1,
      src: "湖北",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 2,
      src: "河北",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 1,
      src: "新疆",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 1,
      src: "黑龙江",
      dst: "广东",
      attack_ip_count: 0,
    },
    {
      c_event_log_count: 1,
      src: "辽宁",
      dst: "广东",
      attack_ip_count: 0,
    },
  ];

  const { coordinateData } = useCoordinateData({
    data: dataValue,
  });

  return (
    <group>
      {coordinateData?.map((item, index) => {
        return (
          <React.Fragment key={index}>
            <RenderRunwayLine item={item} />
            <RenderFlowLine item={item} />
          </React.Fragment>
        );
      })}
    </group>
  );
}
export default FlyLine;
