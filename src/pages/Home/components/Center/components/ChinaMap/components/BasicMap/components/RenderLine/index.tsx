import React, { memo, useMemo } from "react";
import { LineGeometry } from "three/examples/jsm/lines/LineGeometry.js";
import { LineMaterial } from "three/examples/jsm/lines/LineMaterial.js";
import Container from "../../../../container";
import { Line2 } from "three/examples/jsm/lines/Line2.js";
import { transformColor } from "@/utils";

interface IProps {
  points: number[];
}
function RenderLine(props: IProps) {
  const { points } = props;
  const { config, getSideLineMesh, topLinePostion } = Container.useContainer();

  const {
    styleConfig: {
      modelConfig: { lineColor, lineWidth },
      sideLineConfig,
    },
  } = config;

  // 顶线
  // 创建线条的几何和材料
  const topLineMesh = useMemo(() => {
    const geometry = new LineGeometry();
    geometry.setPositions(points);
    const topLineColor = transformColor(lineColor);
    const material = new LineMaterial({
      color: topLineColor.value as any,
      linewidth: lineWidth,
      dashed: false,
      transparent: true,
      opacity: topLineColor.opacity,
    });
    material.resolution.set(window.innerWidth, window.innerHeight);
    const line = new Line2(geometry, material);
    return line;
  }, [lineColor, lineWidth, points]);

  return (
    <React.Fragment>
      {/* 顶线 */}
      <group name="topLine" position={topLinePostion}>
        <primitive object={topLineMesh} />
      </group>
      {sideLineConfig.map((item, index) => {
        return <primitive key={index} object={getSideLineMesh(item, points)} />;
      })}
    </React.Fragment>
  );
}
export default memo(RenderLine);
