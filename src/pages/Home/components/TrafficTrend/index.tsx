import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import type { Coloumns } from "@/components/BasicLine";
import BasicLine from "@/components/BasicLine";
import { useMemo } from "react";
// import MoreComp from "./components/MoreComp";

function TrafficTrend() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const data = [
    {
      name: "20240411",
      value: 60,
      value1: 80,
    },
    {
      name: "20240416",
      value: 40,
      value1: 60,
    },
    {
      name: "20240419",
      value: 40,
      value1: 58,
    },
    {
      name: "20240423",
      value: 80,
      value1: 69,
    },
    {
      name: "20240426",
      value: 70,
      value1: 32,
    },
    {
      name: "20240428",
      value: 50,
      value1: 70,
    },
    {
      name: "20240429",
      value: 50,
      value1: 80,
    },
    {
      name: "20240430",
      value: 40,
      value1: 65,
    },
    {
      name: "20240431",
      value: 40,
      value1: 70,
    },
    {
      name: "20240436",
      value: 45,
      value1: 56,
    },
    {
      name: "20240435",
      value: 45,
      value1: 68,
    },
    {
      name: "20240434",
      value: 30,
      value1: 70,
    },
    {
      name: "20240433",
      value: 60,
      value1: 88,
    },
    {
      name: "20240432",
      value: 110,
      value1: 60,
    },
  ];

  const coloumns = useMemo<Coloumns>(() => {
    return [
      {
        dataKey: "value",
        name: "域名流量",
        color: "rgba(43, 128, 255, 1)",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(43, 128, 255, .4)",
            },
            {
              offset: 1,
              color: "rgba(43, 128, 255, 0)",
            },
          ],
        },
      },
      {
        dataKey: "value1",
        name: "关基流量",
        color: "rgba(4, 205, 244, 1)",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(4, 205, 244, .4)",
            },
            {
              offset: 1,
              color: "rgba(4, 205, 244, 0)",
            },
          ],
        },
      },
    ];
  }, []);

  return (
    <div className="absolute left-[40px] top-[744px] w-[483px]]">
      {/* <MoreComp open={open} onCancel={setFalse} key={visibleKey} /> */}
      <Card headerType={1} title="全网流量走势" onMore={setTrue}>
        <BasicLine data={data} width={470} height={240} coloumns={coloumns} />
      </Card>
    </div>
  );
}
export default TrafficTrend;
