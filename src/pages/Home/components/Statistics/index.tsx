import iconBg4 from "@/assets/1/bg4.png";
import iconBg5 from "@/assets/1/bg5.png";
import icon1 from "@/assets/0/1.png";
import icon2 from "@/assets/1/3.png";
import icon3 from "@/assets/0/2.png";
import bg1 from "@/assets/0/bg1.png";
import bg2 from "@/assets/0/bg2.png";
import React, { useMemo } from "react";
import BgImage from "@/components/BgImage";

function Statistics() {
  const columns = [
    {
      label: "全网攻击",
      value: 60,
      unit: "万",
      icon: icon1,
      id: "1",
    },
    {
      label: "全网事件",
      value: 39.9,
      unit: "万",
      icon: icon2,
      id: "2",
    },
  ];

  // 安全评分值
  const value = 70;

  const angle = useMemo(() => {
    const startAngle = -128;

    const endAngle = 128;

    const angleRange = endAngle - startAngle;

    const percentage = value / 100;

    const angle = startAngle + percentage * angleRange;

    return angle;
  }, []);

  return (
    <React.Fragment>
      <div className="absolute left-[52px] top-[160px] flex">
        <BgImage url={bg1} className="size-[234px] relative">
          <div className="centered">
            <BgImage
              url={bg2}
              className="w-[230px] h-[186px] relative mt-[-10px] flex justify-center"
            >
              <div className="color-secondary text-[18px] x-centered bottom-[10px]">
                安全评分
              </div>
              <div className="text-[#E5C16B] text-[32px] font-[DINCond-Bold] x-centered top-[66px]">
                {value}
              </div>
              <BgImage
                url={icon3}
                className="w-[54px] h-[94px] origin-[center_70%] absolute bottom-[44px]"
                style={{
                  transform: `rotate(${angle}deg)`,
                  transition: "transform 0.5s",
                }}
              />
            </BgImage>
          </div>
        </BgImage>
        <div className="ml-[10px] flex flex-col gap-y-[22px]">
          {columns.map((val) => (
            <div className="flex" key={val.id}>
              <div
                className="w-[106px] h-[106px] flex items-center justify-center"
                style={{
                  backgroundImage: `url(${iconBg4})`,
                }}
              >
                <img src={val.icon} alt="" className="w-[94px] -mt-2" />
              </div>
              <div
                className="w-[114px] h-[106px] text-center"
                style={{
                  backgroundImage: `url(${iconBg5})`,
                }}
              >
                <div className="color-secondary leading-[40px] text-[18px]">
                  {val.label}
                </div>
                <div className="leading-[56px] text-[#E5C16B] text-[32px] font-[DINCond-Bold]">
                  {val.value} {val.unit}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </React.Fragment>
  );
}
export default Statistics;
