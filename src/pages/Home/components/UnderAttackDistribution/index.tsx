import Card from "@/components/Card";
import List from "@/components/List";
import useVisible from "@/hooks/useVisible";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import { useMemo, useState } from "react";

function UnderAttackDistribution() {
  const dataSource = [
    {
      name: "中国",
      count: 1700,
      ratio: 180.1,
    },
    {
      name: "浙江",
      count: 8,
      ratio: 75,
    },
    {
      name: "广东",
      count: 7,
      ratio: -73.1,
    },
    {
      name: "云南",
      count: 2,
      ratio: 95.9,
    },
    {
      name: "上海",
      count: 1,
      ratio: -96.2,
    },
    {
      name: "北京",
      count: 1,
      ratio: 92.2,
    },
    {
      name: "四川",
      count: 1,
      ratio: 92.2,
    },
    {
      name: "江苏",
      count: 1,
      ratio: 88.6,
    },
    {
      name: "安徽",
      count: 1,
      ratio: 66.2,
    },
  ];

  const [activeKey, setActiveKey] = useState("城市");

  const columns = useMemo(() => {
    const value: TableProps["columns"] = [
      {
        title: "序号",
        key: "dataIndex",
        dataIndex: "dataIndex",
        render: (_, __, index) => index + 1,
        width: 60,
        align: "center",
        className: "mr-[90px]",
      },
      {
        title: `${activeKey}名称`,
        dataIndex: "name",
        key: "name",
      },
      {
        title: "事件数量",
        dataIndex: "count",
        key: "count",
        align: "right",
        className: "mr-[30px]",
      },
    ];
    return value;
  }, [activeKey]);

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className="absolute right-[44px] top-[436px] w-[483px] h-[240px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card
        headerType={1}
        title="受攻击分布"
        tabsProps={{
          items: [
            {
              key: "城市",
              label: "城市",
            },
            {
              key: "行业",
              label: "行业",
            },
          ],
          activeKey,
          onChange: setActiveKey,
        }}
        onMore={setTrue}
      >
        <div className="mt-1">
          <List
            width={452}
            height={238}
            data={dataSource}
            size={5}
            columns={columns}
          />
        </div>
      </Card>
    </div>
  );
}
export default UnderAttackDistribution;
