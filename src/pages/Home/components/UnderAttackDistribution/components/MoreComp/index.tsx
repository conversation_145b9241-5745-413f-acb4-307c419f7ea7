import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
    },
    {
      dataIndex: "time",
      title: "事件发生时间",
    },
    {
      dataIndex: "name",
      title: "单位名称",
    },
    {
      dataIndex: "level",
      title: "事件类型",
    },
    {
      dataIndex: "level",
      title: "事件等级",
    },
    {
      dataIndex: "status",
      title: "事件状态",
    },
    {
      dataIndex: "industry",
      title: "单位行业",
    },
    {
      dataIndex: "region",
      title: "事件所属区域",
    },
  ];

  const data = [
    {
      name: "关于防范网站搜索关键词遭恶意利用的风险提示",
      time: "2024-06-20 16:38:02",
      status: "待终审",
      level: "一般事件",
      industry: "政府部门",
      region: "广东省",
    },
    {
      name: "潮州市潮安区疾病预防控制中心",
      time: "2024-06-20 16:38:02",
      status: "待终审",
      level: "一般事件",
      industry: "政府部门",
      region: "广东省",
    },
    {
      name: "汕头市储备粮食和物资有限公司",
      time: "2024-06-20 16:38:02",
      status: "待终审",
      level: "一般事件",
      industry: "政府部门",
      region: "广东省",
    },
    {
      name: "优居集团二手房客户交易数据泄露",
      time: "2024-06-20 16:38:02",
      status: "已完结",
      level: "一般事件",
      industry: "广播电视",
      region: "广东省",
    },
    {
      name: "通天星视频监控平台漏洞风险处置事",
      time: "2024-06-20 16:38:02",
      status: "已完结",
      level: "一般事件",
      industry: "广播电视",
      region: "广东省",
    },
    {
      name: "广东省佛山市某企业办公系统遭反共黑客篡改",
      time: "2024-06-20 16:38:02",
      status: "已完结",
      level: "重大事件",
      industry: "广播电视",
      region: "广东省",
    },
    {
      name: "佛山市南海区教育局所属主机感染僵尸木马",
      time: "2024-06-20 16:38:02",
      status: "已完结",
      level: "重大事件",
      industry: "其他",
      region: "广东省",
    },
    {
      name: "广州公务用车管理平台所属资产与境外IP通联-114",
      time: "2024-06-20 16:38:02",
      status: "已完结",
      level: "重大事件",
      industry: "其他",
      region: "广东省",
    },
    {
      name: "广州公务用车管理平台所属资产与境外IP通联-114",
      time: "2024-06-20 16:38:02",
      status: "已完结",
      level: "重大事件",
      industry: "其他",
      region: "广东省",
    },
    {
      name: "广州公务用车管理平台所属资产与境外IP通联-114",
      time: "2024-06-20 16:38:02",
      status: "已完结",
      level: "重大事件",
      industry: "其他",
      region: "广东省",
    },
  ];

  const loadList: GetProp<typeof ProTable, "request"> = async () => {
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          data: [...data].sort(() => Math.random() - 0.5),
          total: 100,
        });
      }, 300);
    });
  };

  return (
    <ProModal type={2} title="应急指挥实时事件" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
