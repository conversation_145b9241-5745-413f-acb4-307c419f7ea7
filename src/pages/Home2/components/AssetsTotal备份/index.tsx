import BgImage from "@/components/BgImage";
import Card from "@/components/Card";
import icon1 from "@/assets/2/icon1.png";
import icon2 from "@/assets/2/icon2.png";
import icon3 from "@/assets/2/icon3.png";
import icon4 from "@/assets/2/icon4.png";
import Title from "@/components/Title";
import ValueNumber from "@/components/ValueNumber";

function AssetsTotal() {
  return (
    <div className="absolute left-[36px] top-[130px] w-[483px] h-[316px]">
      <Card headerType={1} title="资产总数">
        <div className="flex items-center">
          <div className="color-text text-[18px]">资产总数</div>
          <Title className="ml-2 text-[42px]">
            <ValueNumber dataValue={69788} />
          </Title>
          <div className="color-secondary ml-2 mt-[5px]">/万元</div>
        </div>
        <div className="grid grid-cols-2 gap-y-3 mt-3">
          <div className="flex items-center">
            <BgImage url={icon1} className="size-[74px]" />
            <div className="ml-4">
              <div className="color-secondary my-1">关联单位数</div>
              <div
                className="color-text text-[20px]"
                style={{
                  fontFamily: "DINCond-Bold",
                }}
              >
                <ValueNumber dataValue={7664} />
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <BgImage url={icon2} className="size-[74px]" />
            <div className="ml-4">
              <div className="color-secondary my-1">关联IP资产</div>
              <div
                className="color-text text-[20px]"
                style={{
                  fontFamily: "DINCond-Bold",
                }}
              >
                <ValueNumber dataValue={628765} />
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <BgImage url={icon3} className="size-[74px]" />
            <div className="ml-4">
              <div className="color-secondary my-1">关联单位数</div>
              <div
                className="color-text text-[20px]"
                style={{
                  fontFamily: "DINCond-Bold",
                }}
              >
                <ValueNumber dataValue={12398} />
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <BgImage url={icon4} className="size-[74px]" />
            <div className="ml-4">
              <div className="color-secondary my-1">关联IP资产</div>
              <div
                className="color-text text-[20px]"
                style={{
                  fontFamily: "DINCond-Bold",
                }}
              >
                <ValueNumber dataValue={6224} />
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
export default AssetsTotal;
