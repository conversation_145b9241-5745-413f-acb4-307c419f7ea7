import Card from "@/components/Card";
import bg from "@/assets/2/bg11.png";
import BgImage from "@/components/BgImage";
import ValueNumber from "@/components/ValueNumber";
import icon1 from "@/assets/2/icon5.png";
import icon2 from "@/assets/2/icon6.png";
import icon3 from "@/assets/2/icon7.png";
import icon4 from "@/assets/2/icon8.png";

function AssetsTotal() {
  const columns = [
    {
      title: "网站类",
      value: 17208,
      icon: icon1,
    },
    { title: "生产业务", value: 6039, icon: icon2 },
    { title: "平台类", value: 45267, icon: icon3 },
    { title: "其他", value: 40055, icon: icon4 },
  ];

  return (
    <div className="absolute left-[36px] top-[720px] w-[483px]">
      <Card headerType={1} title="业务类型分布">
        <div className="grid grid-cols-2 gap-y-4 gap-x-4 mt-[20px]">
          {columns.map((item, index) => (
            <BgImage
              key={index + 1}
              url={bg}
              className="w-[200px] h-[106px] flex items-center justify-between"
            >
              <div className="ml-6">
                <div
                  className="color-text  text-[24px]"
                  style={{
                    fontFamily: "DINCond-Bold",
                  }}
                >
                  <ValueNumber dataValue={item.value} />
                </div>
                <div className="text-[16px] color-secondary">{item.title}</div>
              </div>
              <BgImage className="size-[62px] mr-4 mt-3" url={item.icon} />
            </BgImage>
          ))}
        </div>
      </Card>
    </div>
  );
}
export default AssetsTotal;
