import BgImage from "@/components/BgImage";
import Title from "@/components/Title";
import floorPoint from "@/assets/2/floorPoint.png";
import floorBg from "@/assets/2/floorBg.png";

interface IProps {
  children?: React.ReactNode;
}
function TipComp(props: IProps) {
  const { children } = props;

  return (
    <div className="x-centered top-[-110px] flex justify-center flex-col items-center">
      <BgImage
        url={floorBg}
        className="w-[240px] h-[50px] flex justify-center items-center text-[20px]"
      >
        <Title>{children}</Title>
      </BgImage>
      <BgImage url={floorPoint} className=" w-[59px] h-[52px] mt-2" />
    </div>
  );
}
export default TipComp;
