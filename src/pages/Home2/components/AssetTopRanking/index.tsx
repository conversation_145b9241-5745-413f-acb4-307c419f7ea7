import Card from "@/components/Card";
import Chart from "./components/Chart";

function AssetTopRanking() {
  const data = [
    {
      name: "广州",
      value: 6000,
    },
    {
      name: "深圳",
      value: 3000,
    },
    {
      name: "惠州",
      value: 2158,
    },
    {
      name: "佛山",
      value: 1796,
    },
    {
      name: "珠海",
      value: 1579,
    },
    {
      name: "汕头",
      value: 1281,
    },
    {
      name: "东莞",
      value: 1252,
    },
    {
      name: "茂名",
      value: 1192,
    },
    {
      name: "湛江",
      value: 1176,
    },
    {
      name: "清远",
      value: 1173,
    },
  ];

  return (
    <div className="absolute left-[542px] top-[720px] w-[826px]">
      <Card headerType={2} title="地市资产排名Top10">
        <Chart width={800} height={256} data={data} />
      </Card>
    </div>
  );
}
export default AssetTopRanking;
