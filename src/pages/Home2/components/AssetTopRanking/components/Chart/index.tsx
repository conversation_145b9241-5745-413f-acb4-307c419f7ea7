import type { ContentType } from "recharts/types/component/Tooltip";
import type {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import type { LinearConfig } from "@/typings";
import { nanoid } from "nanoid";
import { useInterval } from "ahooks";
import { useCallback, useMemo, useRef, useState } from "react";
import { Bar, BarChart, CartesianGrid, Tooltip, XAxis, YAxis } from "recharts";
import SvgLinearGradient from "@/components/Comp/SvgLinearGradient";
import tipArrow from "@/assets/2/tipArrow.png";
import BgImage from "@/components/BgImage";

interface DataItem {
  name: string;

  value: number;
}

interface IProps {
  data: DataItem[];

  width: number;

  height: number;
}

interface ColorConfig {
  top: LinearConfig;
  bottom: LinearConfig;
  center: LinearConfig;
}

function Chart(props: IProps) {
  const { data, width, height } = props;
  const customXTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;
    return (
      <g>
        <foreignObject
          style={{
            overflow: "visible",
          }}
          width="1"
          height="1"
          x={x}
          y={y + 5}
        >
          <div className="color-chart">
            <div
              style={{
                width: "fit-content",
                transform: `translate(-50%, 0px) rotate(0deg)`,
                textAlign: "center",
              }}
              className="truncate w-[40px]"
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const customYTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;

    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={x}
          y={y}
          style={{
            overflow: "visible",
          }}
        >
          <div className="color-chart">
            <div
              style={{
                width: "max-content",
                transform: `translate(-100%, -50%)`,
                textAlign: "right",
              }}
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const filterIdPrefix = useRef(nanoid());

  const colorList = useMemo(() => {
    const value: ColorConfig[] = [
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(253, 148, 95, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(247, 79, 64, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(253, 148, 95, 1)",
            },
            {
              offset: 1,
              color: "rgba(247, 79, 64, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(249, 93, 88, 1)",
            },
            {
              offset: 1,
              color: "rgba(253, 154, 133, 0)",
            },
          ],
        },
      },
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(247, 68, 168, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(215, 24, 80, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(247, 68, 168, 1)",
            },
            {
              offset: 1,
              color: "rgba(215, 24, 80, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(222, 34, 100, 1)",
            },
            {
              offset: 1,
              color: "rgba(249, 88, 191, 0)",
            },
          ],
        },
      },
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(193, 99, 247, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(174, 70, 247, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(193, 99, 247, 1)",
            },
            {
              offset: 1,
              color: "rgba(174, 70, 247, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(181, 77, 246, 1)",
            },
            {
              offset: 1,
              color: "rgba(204, 111, 248, 0)",
            },
          ],
        },
      },
      {
        top: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(63, 168, 242, 0.5)",
            },
            {
              offset: 1,
              color: "rgba(21, 100, 232, 0.5)",
            },
          ],
        },
        bottom: {
          angle: 90,
          colorStops: [
            {
              offset: 0,
              color: "rgba(63, 168, 242, 1)",
            },
            {
              offset: 1,
              color: "rgba(21, 100, 232, 1)",
            },
          ],
        },
        center: {
          angle: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(27, 110, 233, 1)",
            },
            {
              offset: 1,
              color: "rgba(66, 170, 242, 0)",
            },
          ],
        },
      },
    ];
    return value;
  }, []);

  const barRef = useRef(new Map<string, number>());

  const CustomBarShape = useCallback(
    (props: any) => {
      const shapeProps = props.shapeProps;
      const { x, y, width, height, index, name } = shapeProps;

      const skewAngle = 0; // 你的 skew 角度

      const verticalOffset =
        (width / 2) * Math.tan((skewAngle * Math.PI) / 180);

      const ellipseHeight = width / 4;

      const idIndex = index < 3 ? index : colorList.length - 1;

      const topFill = `url(#${filterIdPrefix.current}-${idIndex}-top)`;

      const centerFill = `url(#${filterIdPrefix.current}-${idIndex}-center)`;

      const bottomFill = `url(#${filterIdPrefix.current}-${idIndex}-bottom)`;

      barRef.current.set(name, height);

      return (
        <g>
          <g>
            <ellipse
              cx={x + width / 2}
              cy={y - verticalOffset * 2}
              rx={width / 2}
              ry={ellipseHeight}
              fill={topFill}
            ></ellipse>
            <rect
              width={width}
              height={height}
              x={x}
              y={y - verticalOffset}
              fill={centerFill}
            ></rect>
            <ellipse
              cx={x + width / 2}
              cy={y + height - verticalOffset * 2}
              rx={width / 2}
              ry={ellipseHeight}
              fill={bottomFill}
            ></ellipse>
          </g>
        </g>
      );
    },
    [colorList.length]
  );

  // 轴单位
  const customizedLabel = useCallback((props) => {
    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={props.viewBox.x}
          y={props.viewBox.y}
          style={{
            overflow: "visible",
          }}
        >
          <div
            style={{
              width: "max-content",
              transform: "translate(-20px, -40px)",
            }}
            className="color-chart"
          >
            (单位)
          </div>
        </foreignObject>
      </g>
    );
  }, []);

  const tooltipContent: ContentType<ValueType, NameType> = (props) => {
    let { payload = [], coordinate, label, viewBox } = props;
    if (payload?.length > 0 && label && viewBox?.height) {
      const barHeight = barRef.current.get(label) ?? 0;
      const value = payload[0].value;

      const xValue = coordinate?.x ?? 1;

      return (
        <div
          className="w-[148px] -translate-x-1/2 flex flex-col items-center"
          style={{
            marginLeft: xValue - 1,
            marginTop: viewBox?.height - barHeight - 44,
          }}
        >
          <div
            className="flex items-center justify-center h-[44px] color-text gap-x-2 bg-[rgba(50,91,174,0.4)] rounded-[8px] overflow-hidden px-8"
            style={{
              boxShadow: "inset 0px 0 20px 0px #5FC1FF",
              backdropFilter: "blur(12px)",
            }}
          >
            <span className="text-[17px]">{label}</span>
            <span
              className="text-[22px]"
              style={{
                fontFamily: "DINCond-Bold",
              }}
            >
              {value}
            </span>
          </div>
          <BgImage url={tipArrow} className="w-[26px] h-[21px] mt-2" />
        </div>
      );
    }

    return null;
  };

  const [defaultIndex, setDefaultIndex] = useState<number | undefined>(
    undefined
  );

  // 悬浮中 暂停轮播
  const [isHovering, setIsHovering] = useState(false);

  useInterval(
    () => {
      setDefaultIndex((val) => {
        if (val === undefined) {
          return 0;
        }

        const total = data.length ?? 0;

        return (val + 1) % total;
      });
    },
    isHovering ? undefined : 2500
  );

  return (
    <div
      className="w-full h-full"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <BarChart
        width={width}
        height={height}
        data={data}
        className="text-[14px]"
        margin={{
          top: 50,
          right: 45,
          left: 20,
          bottom: 10,
        }}
        barSize={20}
      >
        <CartesianGrid vertical={false} stroke="rgba(168, 168, 168, 0.2)" />
        <XAxis
          dataKey="name"
          tick={customXTick}
          tickLine={false}
          axisLine={{
            stroke: "rgba(201, 201, 201, 0.55)",
          }}
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tick={customYTick}
          width={30}
          label={customizedLabel}
        />
        {colorList.map((val, index) => {
          const idPrefix = `${filterIdPrefix.current}-${index}`;
          return (
            <defs key={idPrefix}>
              <SvgLinearGradient id={`${idPrefix}-top`} config={val.top} />
              <SvgLinearGradient
                id={`${idPrefix}-center`}
                config={val.center}
              />
              <SvgLinearGradient
                id={`${idPrefix}-bottom`}
                config={val.bottom}
              />
            </defs>
          );
        })}
        <Bar
          dataKey="value"
          barSize={30}
          fill="#1DB7FA"
          shape={(props: any) => {
            return <CustomBarShape shapeProps={props} />;
          }}
          label={false}
          onAnimationEnd={() => {
            // setIsHovering(false);
            setDefaultIndex(0);
          }}
        />
        <Tooltip
          cursor={false}
          trigger="hover"
          active
          defaultIndex={defaultIndex}
          content={tooltipContent}
          allowEscapeViewBox={{
            x: true,
            y: true,
          }}
          wrapperStyle={{
            transform: "none",
          }}
          isAnimationActive={false}
        />
      </BarChart>
    </div>
  );
}
export default Chart;
