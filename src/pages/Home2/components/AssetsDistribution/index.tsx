import Card from "@/components/Card";
import { motion } from "framer-motion";
import useListSize from "@/hooks/useListSize";
import useListAnimate from "@/hooks/useListAnimate";
import {
  TableAnimationConnectModeEnum,
  TableAnimationModeEnum,
  TableAnimationTypeEnum,
} from "@/enum";
import { useMemo } from "react";
import ListItem from "./components/ListItem";

function AssetsDistribution() {
  const data = useMemo(() => {
    return [
      {
        name: "政府部门",
        value: 7000,
      },
      {
        name: "金融",
        value: 5000,
      },
      {
        name: "电信",
        value: 4522,
      },
      {
        name: "其他",
        value: 2946,
      },
      {
        name: "能源",
        value: 2228,
      },
      {
        name: "教育",
        value: 2023,
      },
      {
        name: "民航",
        value: 1834,
      },
      {
        name: "社会保障",
        value: 694,
      },
      {
        name: "卫生健康",
        value: 642,
      },
      {
        name: "应急管理",
        value: 538,
      },
      {
        name: "公路水路运输",
        value: 181,
      },
      {
        name: "广播电视",
        value: 85,
      },
      {
        name: "邮政",
        value: 34,
      },
      {
        name: "水利",
        value: 21,
      },
      {
        name: "铁路",
        value: 12,
      },
      {
        name: "国防科工",
        value: 1,
      },
    ];
  }, []);

  const headerHeight = 40;

  const height = 840;

  const size = 12;

  const rowGap = 12;

  const width = 456;

  const { itemHeight, listHeight } = useListSize({
    headerHeight,
    height,
    size,
    rowGap,
  });

  const animationConfig = {
    show: true, // 是否显示动画
    type: TableAnimationTypeEnum.Single, // 类型
    connectMode: TableAnimationConnectModeEnum.Continuous, // 衔接方式
    animateMode: TableAnimationModeEnum.Flip, // 动画形式
    interval: 3, //
    backgroundFixed: false,
  };

  const {
    controls,
    firstControls,
    dataIndexList,
    showAnimate,
    beforeIndexList,
  } = useListAnimate({
    data,
    size,
    rowGap,
    listHeight,
    itemHeight,
    animationConfig,
  });

  const maxValue = useMemo(() => {
    const countList = data?.map((val) => val.value) ?? [];
    return Math.max(...countList);
  }, [data]);

  return (
    <div className="absolute right-[46px] top-[130px] w-[483px] h-[540px]">
      <Card headerType={1} title="行业资产分布">
        <div
          style={{
            width,
            height,
          }}
          className="relative -top-2"
        >
          <div
            className="flex color-secondary items-center gap-x-3"
            style={{
              height: headerHeight,
            }}
          >
            <div className="w-[76px]">序号</div>
            <div className="flex-1">行业名称</div>
            <div className="w-[64px]">资产数</div>
          </div>
          {/* 列表 */}
          <div
            className="overflow-hidden relative"
            style={{
              height: listHeight,
              width: width + 40, // 为了进度图标能显示出来
            }}
          >
            <motion.div
              className="absolute"
              style={{
                width,
                height: listHeight,
              }}
              animate={controls}
            >
              {showAnimate && (
                <motion.div animate={firstControls}>
                  {beforeIndexList?.map((val) => (
                    <ListItem
                      key={`before${val}`}
                      index={val}
                      rowGap={rowGap}
                      item={data[val]}
                      showAnimate={false}
                      max={maxValue}
                      itemHeight={itemHeight}
                    />
                  ))}
                </motion.div>
              )}
              {dataIndexList.map((val) => (
                <ListItem
                  key={`listItem${val + 1}`}
                  index={val}
                  rowGap={rowGap}
                  item={data[val]}
                  max={maxValue}
                  itemHeight={itemHeight}
                />
              ))}
            </motion.div>
          </div>
        </div>
      </Card>
    </div>
  );
}
export default AssetsDistribution;
