import BgImage from "@/components/BgImage";
import Card from "@/components/Card";
import icon1 from "@/assets/2/icon1.png";
import icon2 from "@/assets/2/icon2.png";
import icon3 from "@/assets/2/icon3.png";
import icon4 from "@/assets/2/icon4.png";
import Title from "@/components/Title";
import titleBg from "@/assets/2/bg8.png";
import bg10 from "@/assets/2/bg10.png";
import ValueNumber from "@/components/ValueNumber";

function AssetsTotal() {
  const columns = [
    {
      title: "关联单位数",
      value: 7664,
      icon: icon1,
    },
    { title: "关基IP资产", value: 61293, icon: icon2 },
    { title: "关基设施数", value: 12386, icon: icon3 },
    { title: "关基域名资产", value: 6224, icon: icon4 },
  ];

  return (
    <div className="absolute left-[36px] top-[122px] w-[483px] h-[316px]">
      <Card headerType={1} title="资产总数">
        <div className="relative -left-[10px]">
          <BgImage
            url={titleBg}
            className="flex items-center w-[432px] h-[60px] pl-[24px] mt-2"
          >
            <div className="color-text text-[18px] font-bold">资产总数</div>
            <Title className="ml-4 text-[42px]">
              <ValueNumber dataValue={69788} />
            </Title>
            <div className="color-secondary ml-2 mt-[5px]"></div>
          </BgImage>
          <div className="grid grid-cols-2 gap-y-3 gap-x-6 mt-3 w-[432px] pl-2">
            {columns.map((val, index) => (
              <BgImage
                key={index + 1}
                url={bg10}
                className="flex items-center relative px-3 rounded-[1px] overflow-hidden h-[70px]"
              >
                <BgImage url={val.icon} className="size-[52px]" />
                <div className="ml-3 mt-[2px]">
                  <div className="color-secondary">{val.title}</div>
                  <div
                    className="color-text text-[20px]"
                    style={{
                      fontFamily: "DINCond-Bold",
                    }}
                  >
                    <ValueNumber dataValue={val.value} />
                  </div>
                </div>
              </BgImage>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
}
export default AssetsTotal;
