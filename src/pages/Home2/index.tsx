import BgImage from "@/components/BgImage";
import bg from "@/assets/2/bg.webp";
import AssetsTotal from "./components/AssetsTotal";
import AssetsDistribution from "./components/AssetsDistribution";
import AssetsType from "./components/AssetsType";
import BusinessType from "./components/BusinessType";
import AssetTopRanking from "./components/AssetTopRanking";
import StatisticsCount from "./components/StatisticsCount";

function Home2() {
  return (
    <BgImage url={bg} className="absolute inset-x-0 inset-y-0 -z-[10px]">
      {/* 资产总数 */}
      <AssetsTotal />
      {/* 资产类型分类 */}
      <AssetsType />
      {/* 业务类型分布 */}
      <BusinessType />
      {/* 行业资产分布 */}
      <AssetsDistribution />
      {/* 地市资产排行Top10 */}
      <AssetTopRanking />
      {/* 中间的统计数量 */}
      <StatisticsCount />
    </BgImage>
  );
}
export default Home2;
