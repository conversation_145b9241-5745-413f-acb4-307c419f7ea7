/**
 * table动画类型
 */
export enum TableAnimationTypeEnum {
  /**
   * 全部
   */
  All = "all",

  /**
   * 逐条
   */
  Single = "single",
}

/**
 * table衔接方式
 */
export enum TableAnimationConnectModeEnum {
  /**
   * 连续
   */
  Continuous = "continuous",

  /**
   * 从头开始
   */
  FromStart = "fromStart",
}

/**
 * table动画形式
 */
export enum TableAnimationModeEnum {
  /**
   * 从上而下
   */
  TopToBottom = "topToBottom",

  /**
   * 翻牌
   */
  Flip = "flip",
}

/**
 * 日期类型
 */
export enum DateTypeEnum {
  /**
   * 24H
   */
  Day = "day",

  /**
   * 7天
   */
  Week = "week",

  /**
   * 30天
   */
  Month = "month",

  /**
   * 自定义
   */
  Custom = "custom",
}

/**
 * 地图类型
 */
export enum MapTypeEnum {
  /**
   * 世界地图
   */
  World = "world",

  /**
   * 中国地图
   */
  China = "china",

  /**
   * 广东地图
   */
  Guangdong = "guangdong",
}

export enum ProjectionTypeEnum {
  /**
   * 墨卡托投影
   */
  Mercator = "mercator",

  /**
   * WGS84
   */
  WGS84 = "wgs84",
}

/**
 * 地图各模型对应的基础层级枚举 0为坐标点z的0 按照缩小100倍计算
 */
export enum MapLevelEnum {
  /**
   * 基础地图层级
   */
  BASIC_LEVEL = 0.001,

  /**
   * 文本基础层级
   */
  BASIC_LABEL_LEVEL = 0.004,

  /**
   * 顶线基础层级
   */
  BASIC_TOP_LINE_LEVEL = 0.002,

  /**
   * 底线基础层级
   */
  BASIC_BOTTOM_LINE_LEVEL = 0.004,
}
