import bg from "@/assets/tabBg.png";
import bgActive from "@/assets/tabActiveBg.png";
import BgImage from "@/components/BgImage";
import { DateTypeEnum } from "@/enum";
import dayjs from "dayjs";
import { useMemo, useState } from "react";
import styles from "./index.module.less";
import { DatePicker } from "antd";
import React from "react";
import Title from "@/components/Title";

const { RangePicker } = DatePicker;

function DateQuery() {
  const options = [
    {
      key: DateTypeEnum.Day,
      label: "24H",
    },
    {
      key: DateTypeEnum.Week,
      label: "7天",
    },
    {
      key: DateTypeEnum.Month,
      label: "30天",
    },
    {
      key: DateTypeEnum.Custom,
      label: "自定义",
    },
  ];

  const [startTime, setStartTime] = useState<string | undefined>(
    dayjs().add(-1, "day").format("YYYY-MM-DD")
  );

  const [endTime, setEndTime] = useState<string | undefined>(
    dayjs().format("YYYY-MM-DD")
  );

  const [open, setOpen] = useState(false);

  const activeKey = useMemo(() => {
    const startDayJs = dayjs(startTime);
    const endDayJs = dayjs(endTime);
    const differenceInDays = endDayJs.diff(startDayJs, "day");
    switch (differenceInDays) {
      case 1:
        return DateTypeEnum.Day;
      case 7:
        return DateTypeEnum.Week;
      case 30:
        return DateTypeEnum.Month;

      default:
        return DateTypeEnum.Custom;
    }
  }, [endTime, startTime]);

  const onSelect = (key: DateTypeEnum) => {
    if (key === DateTypeEnum.Day) {
      setStartTime(dayjs().add(-1, "day").format("YYYY-MM-DD"));
      setEndTime(dayjs().format("YYYY-MM-DD"));
    } else if (key === DateTypeEnum.Week) {
      setStartTime(dayjs().add(-7, "day").format("YYYY-MM-DD"));
      setEndTime(dayjs().format("YYYY-MM-DD"));
    } else if (key === DateTypeEnum.Month) {
      setStartTime(dayjs().add(-30, "day").format("YYYY-MM-DD"));
      setEndTime(dayjs().format("YYYY-MM-DD"));
    } else {
      setOpen(true);
    }
  };

  return (
    <React.Fragment>
      <div className="absolute top-[65px] w-[320px] right-[45px] z-[100]">
        <div className="flex justify-end">
          {options.map((val) => (
            <BgImage
              url={activeKey === val.key ? bgActive : bg}
              key={val.key}
              onClick={(e) => {
                e.stopPropagation();
                onSelect(val.key);
              }}
              className={`h-[36px] w-[74px] flex items-center justify-center cursor-pointer ${styles.item}`}
            >
              {val.label}
            </BgImage>
          ))}
        </div>
        <Title className="text-[16px] cursor-pointer ml-9 mt-2">
          {startTime} ~ {endTime}
        </Title>
      </div>
      <RangePicker
        onOpenChange={setOpen}
        open={open}
        onChange={(_, dateString) => {
          setStartTime(dateString[0]);
          setEndTime(dateString[1]);
        }}
        className="absolute right-[26px] opacity-0 top-[64px] -z-10 w-[80px]"
      ></RangePicker>
    </React.Fragment>
  );
}
export default DateQuery;
