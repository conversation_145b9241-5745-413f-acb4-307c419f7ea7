import selectBg from "@/assets/selectBg.png";
import { Select } from "antd";
import { DownOutlined } from "@ant-design/icons";
import dictInfo from "@/dictInfo";
import { history, useLocation } from "umi";

function PageSelect() {
  const { pathname } = useLocation();

  const onChange = (value: string) => {
    history.push(value);
  };

  return (
    <div className="absolute top-[65px] left-[45px] z-[100]">
      <Select
        className="w-[228px] h-[48px]"
        value={pathname}
        onChange={onChange}
        style={{
          background: `url(${selectBg})`,
          fontWeight: "bold",
        }}
        labelRender={(label) => (
          <div className="flex w-full justify-center gap-x-2 items-center text-[18px] pl-2 font-bold">
            <span>{label.label}</span>
            <DownOutlined className="text-[14px]" />
          </div>
        )}
        getPopupContainer={(triggerNode) => triggerNode.parentElement!}
        suffixIcon={false}
        variant="borderless"
        options={dictInfo.pageType}
      ></Select>
    </div>
  );
}
export default PageSelect;
