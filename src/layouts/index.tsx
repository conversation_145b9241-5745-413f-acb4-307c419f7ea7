import { Outlet, useLocation } from "umi";
import { ConfigProvider, theme } from "antd";
import PageSelect from "./components/PageSelect";
import DateQuery from "./components/DateQuery";
import ReactScaleScreen from "@/components/ReactScaleScreen";
import bgUrl from "@/assets/borderBg.webp";
import zhCN from "antd/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { useMemo, useRef } from "react";
import Title from "@/components/Title";

export default function Layout() {
  const domRef = useRef<HTMLDivElement>(null);

  const { pathname } = useLocation();

  const bgColor = useMemo(() => {
    if (pathname === "/home") {
      return "#07101B";
    }
    return "#030f1f";
  }, [pathname]);

  return (
    <ConfigProvider
      theme={{
        token: {
          fontSize: 16,
          colorTextBase: "#daedff",
          colorPrimary: "#395ABF",
        },
        components: {
          Select: {
            selectorBg: "transparent",
            optionPadding: "8px 12px",
            optionSelectedBg: "rgba(57,90,191,.65)",
            // optionActiveBg:"#131F30"
          },
          DatePicker: {
            cellHeight: 34,
            cellWidth: 34,
            paddingXXS: 2,
            cellActiveWithRangeBg: "rgba(16, 55, 105, 0.65)",
            colorBgElevated:
              "linear-gradient(0deg, rgba(2,29,58,.96) 0%, rgba(2,48,84,.96) 100%)",
          },
          Tooltip: {
            colorBgSpotlight: "rgba(76,146,217,.9)",
          },
        },
        algorithm: theme.darkAlgorithm,
      }}
      getPopupContainer={() => domRef.current!}
      locale={zhCN}
    >
      <div
        className="w-sreen h-screen relative"
        style={{
          backgroundColor: bgColor,
        }}
      >
        <ReactScaleScreen>
          <PageSelect />
          <DateQuery />
          <Outlet />
          <div className="relative w-0 h-0" ref={domRef}></div>
          <div
            className="absolute inset-x-0 inset-y-0 bg-cover pointer-events-none"
            style={{
              backgroundImage: `url(${bgUrl})`,
            }}
          >
            <Title className="text-[50px] flex justify-center items-center mt-[10px]">
              省委网信办广东省网络安全综合态势感知平台
            </Title>
          </div>
        </ReactScaleScreen>
      </div>
    </ConfigProvider>
  );
}
