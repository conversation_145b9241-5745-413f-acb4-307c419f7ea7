import { defineConfig } from "umi";

const isDev = process.env.NODE_ENV === "development";

export default defineConfig({
  routes: [
    { path: "/", redirect: "/home1" },
    {
      path: "/home",
      component: "Home",
    },
    {
      path: "/home1",
      component: "Home1",
    },
    {
      path: "/home2",
      component: "Home2",
    },
  ],
  // base:"./",
  publicPath: isDev ? "/" : "./",
  mfsu: false,
  npmClient: "pnpm",
  hash: true,
  history: {
    type: "hash",
  },
  tailwindcss: {},
  plugins: ["@umijs/plugins/dist/tailwindcss"],
});
