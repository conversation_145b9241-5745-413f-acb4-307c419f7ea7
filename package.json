{"name": "app", "private": true, "author": "王明远 <<EMAIL>>", "scripts": {"dev": "PORT=8887 umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@ant-design/pro-table": "^3.16.1", "@react-three/drei": "9.92.4", "@react-three/fiber": "^8.13.6", "@turf/turf": "^7.0.0", "@types/d3-geo": "^3.1.0", "@types/lodash-es": "^4.17.12", "@types/tinycolor2": "^1.4.6", "ahooks": "^3.8.0", "antd": "^5.18.1", "d3-geo": "^3.1.1", "dayjs": "^1.11.11", "framer-motion": "^11.2.10", "gsap": "^3.12.5", "lodash-es": "^4.17.21", "nanoid": "^5.0.7", "react": "^18.3.1", "recharts": "^2.12.7", "three": "0.159.0", "three-stdlib": "^2.30.3", "tinycolor2": "^1.6.0", "umi": "^4.2.5", "unstated-next": "^1.1.0", "use-count-up": "^3.0.1"}, "devDependencies": {"@react-three/eslint-plugin": "^0.1.1", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/three": "^0.165.0", "@umijs/lint": "4.0.47", "@umijs/plugins": "^4.1.10", "eslint": "8.33.0", "stylelint": "^14", "tailwindcss": "^3", "typescript": "^5.0.3"}}