@tailwind base;
@tailwind components;
@tailwind utilities;

.centered{
    @apply absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2;
}

.x-centered{
    @apply absolute left-1/2 -translate-x-1/2;
}

.y-centered{
    @apply absolute top-1/2 -translate-y-1/2;
}

.animation-direction-alternate{
    animation-direction: alternate;
}

.color-secondary{
    color: #8B969E;
}

.color-text{
    color: #C5D0D4;
}

.color-chart{
    color: rgba(201,201,201,.8);
}
