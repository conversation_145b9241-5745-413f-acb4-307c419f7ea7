@font-face {
  font-family: "庞门正道标题体";
  src: url("./fonts/庞门正道标题体.ttf");
}

@font-face {
  font-family: "优设标题黑";
  src: url("./fonts/优设标题黑.ttf");
}

@font-face {
  font-family: "站酷酷黑体";
  src: url("./fonts/站酷酷黑体.ttf");
}

@font-face {
  font-family: "AlibabaSans-Black";
  src: url("./fonts/AlibabaSans-Black.otf");
}

@font-face {
  font-family: "AlibabaSans-Bold";
  src: url("./fonts/AlibabaSans-Bold.otf");
}

@font-face {
  font-family: "AlibabaSans-BoldItalic";
  src: url("./fonts/AlibabaSans-BoldItalic.otf");
}

@font-face {
  font-family: "AlibabaSans-Heavy";
  src: url("./fonts/AlibabaSans-Heavy.otf");
}

@font-face {
  font-family: "AlibabaSans-HeavyItalic";
  src: url("./fonts/AlibabaSans-HeavyItalic.otf");
}

@font-face {
  font-family: "AlibabaSans-Italic";
  src: url("./fonts/AlibabaSans-Italic.otf");
}

@font-face {
  font-family: "AlibabaSans-Light";
  src: url("./fonts/AlibabaSans-Light.otf");
}

@font-face {
  font-family: "AlibabaSans-LightItalic";
  src: url("./fonts/AlibabaSans-LightItalic.otf");
}

@font-face {
  src: url("./fonts/AlibabaSans-Medium.otf");
  font-family: "AlibabaSans-Medium";
}

@font-face {
  src: url("./fonts/AlibabaSans-MediumItalic.otf");
  font-family: "AlibabaSans-MediumItalic";
}

@font-face {
  src: url("./fonts/AlibabaSans-Regular.otf");
  font-family: "AlibabaSans-Regular";
}

@font-face {
  src: url("./fonts/DINCond-Bold.otf");
  font-family: "DINCond-Bold";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Black.ttf");
  font-family: "HarmonyOS_Sans_Black";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Black_Italic_0.ttf");
  font-family: "HarmonyOS_Sans_Black_Italic_0";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Bold.ttf");
  font-family: "HarmonyOS_Sans_Bold";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Bold_Italic_0.ttf");
  font-family: "HarmonyOS_Sans_Bold_Italic_0";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Light_Italic_0.ttf");
  font-family: "HarmonyOS_Sans_Light_Italic_0";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Light.ttf");
  font-family: "HarmonyOS_Sans_Light";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Medium_Italic_0.ttf");
  font-family: "HarmonyOS_Sans_Medium_Italic_0";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Medium.ttf");
  font-family: "HarmonyOS_Sans_Medium";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Regular_Italic_0.ttf");
  font-family: "HarmonyOS_Sans_Regular_Italic_0";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Regular.ttf");
  font-family: "HarmonyOS_Sans_Regular";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Thin_Italic_0.ttf");
  font-family: "HarmonyOS_Sans_Thin_Italic_0";
}

@font-face {
  src: url("./fonts/HarmonyOS_Sans_Thin.ttf");
  font-family: "HarmonyOS_Sans_Thin";
}

@font-face {
  src: url("./fonts/LCD-BOLD-5.ttf");
  font-family: "LCD-BOLD-5";
}

@font-face {
  src: url("./fonts/LCD-L-6.ttf");
  font-family: "LCD-L-6";
}

@font-face {
  src: url("./fonts/LCD-N-7.ttf");
  font-family: "LCD-N-7";
}

@font-face {
  src: url("./fonts/LCD-U-8.ttf");
  font-family: "LCD-U-8";
}

@font-face {
  src: url("./fonts/LCD2B-1.ttf");
  font-family: "LCD2B-1";
}

@font-face {
  src: url("./fonts/LCD2L-2.ttf");
  font-family: "LCD2L-2";
}

@font-face {
  src: url("./fonts/LCD2N-3.ttf");
  font-family: "LCD2N-3";
}

@font-face {
  src: url("./fonts/LCD2U-4.ttf");
  font-family: "LCD2U-4";
}

@font-face {
  src: url("./fonts/LCDM2B-9.ttf");
  font-family: "LCDM2B-9";
}

@font-face {
  src: url("./fonts/LCDM2L-10.ttf");
  font-family: "LCDM2L-10";
}

@font-face {
  src: url("./fonts/LCDM2N-11.ttf");
  font-family: "LCDM2N-11";
}

@font-face {
  src: url("./fonts/LCDM2U-12.ttf");
  font-family: "LCDM2U-12";
}

@font-face {
  src: url("./fonts/LCDMB-13.ttf");
  font-family: "LCDMB-13";
}

@font-face {
  src: url("./fonts/LCDML-14.ttf");
  font-family: "LCDML-14";
}

@font-face {
  src: url("./fonts/LCDMN-15.ttf");
  font-family: "LCDML-15";
}

@font-face {
  src: url("./fonts/LCDMU-16.ttf");
  font-family: "LCDMU-16";
}

@font-face {
  src: url("./fonts/YouSheBiaoTiHei-2.ttf");
  font-family: "YouSheBiaoTiHei-2";
}

@font-face {
  src: url("./fonts/gilroy-black-6.otf");
  font-family: "gilroy-black-6";
}

@font-face {
  src: url("./fonts/gilroy-blackitalic-7.otf");
  font-family: "gilroy-blackitalic-7";
}

@font-face {
  src: url("./fonts/gilroy-bold-4.otf");
  font-family: "gilroy-bold-4";
}

@font-face {
  src: url("./fonts/Gilroy-ExtraBoldItalic-10.otf");
  font-family: "Gilroy-ExtraBoldItalic-10";
}

@font-face {
  src: url("./fonts/Gilroy-HeavyItalic-8.otf");
  font-family: "Gilroy-HeavyItalic-8";
}

@font-face {
  src: url("./fonts/Gilroy-Light-11.otf");
  font-family: "Gilroy-Light-11";
}

@font-face {
  src: url("./fonts/Gilroy-LightItalic-12.otf");
  font-family: "Gilroy-LightItalic-12";
}

@font-face {
  src: url("./fonts/Gilroy-Medium-2.otf");
  font-family: "Gilroy-Medium-2";
}

@font-face {
  src: url("./fonts/gilroy-regular-3.otf");
  font-family: "gilroy-regular-3";
}

@font-face {
  src: url("./fonts/Gilroy-Thin-13.otf");
  font-family: "Gilroy-Thin-13";
}

@font-face {
  src: url("./fonts/Gilroy-ThinItalic-14.otf");
  font-family: "Gilroy-ThinItalic-14";
}

@font-face {
  src: url("./fonts/Radomir-Tinkov-Gilroy-Heavy-9.otf");
  font-family: "Radomir-Tinkov-Gilroy-Heavy-9";
}

@font-face {
  src: url("./fonts/manrope-bold-2.otf");
  font-family: "manrope-bold-2";
}

@font-face {
  src: url("./fonts/manrope-extrabold-3.otf");
  font-family: "manrope-extrabold-3";
}

@font-face {
  src: url("./fonts/manrope-light-4.otf");
  font-family: "manrope-light-4";
}

@font-face {
  src: url("./fonts/manrope-medium-5.otf");
  font-family: "manrope-medium-5";
}

@font-face {
  src: url("./fonts/manrope-regular-6.otf");
  font-family: "manrope-regular-6";
}

@font-face {
  src: url("./fonts/manrope-semibold-7.otf");
  font-family: "manrope-semibold-7";
}

@font-face {
  src: url("./fonts/manrope-thin-8.otf");
  font-family: "manrope-thin-8";
}

@font-face {
  src: url("./fonts/Radomir-Tinkov-Gilroy-Heavy-9.otf");
  font-family: "Radomir-Tinkov-Gilroy-Heavy-9";
}

@font-face {
  src: url("./fonts/YouSheBiaoTiHei-2.ttf");
  font-family: "YouSheBiaoTiHei-2";
}
